import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const resolvedParams = await params;
    const relatorioId = resolvedParams.id;
    const { searchParams } = new URL(request.url);
    const formato = searchParams.get('formato') || 'json';
    const periodo = searchParams.get('periodo') || '30';

    const csvReader = new CSVReader();
    const processos = await csvReader.lerProcessos();

    let dadosRelatorio;

    switch (relatorioId) {
      case 'desempenho':
        dadosRelatorio = await gerarRelatorioDesempenho(processos, periodo);
        break;
      
      case 'tempo-medio':
        dadosRelatorio = await gerarRelatorioTempoMedio(processos, periodo);
        break;
      
      case 'secretarias':
        dadosRelatorio = await gerarRelatorioSecretarias(processos, periodo);
        break;
      
      case 'compliance':
        dadosRelatorio = await gerarRelatorioCompliance(processos, periodo);
        break;
      
      case 'financeiro':
        dadosRelatorio = await gerarRelatorioFinanceiro(processos, periodo);
        break;
      
      default:
        return NextResponse.json(
          { success: false, error: 'Relatório não encontrado' },
          { status: 404 }
        );
    }

    if (formato === 'pdf') {
      return await gerarPDF(dadosRelatorio, relatorioId);
    }

    if (formato === 'excel') {
      return await gerarExcel(dadosRelatorio, relatorioId);
    }

    return NextResponse.json({
      success: true,
      data: dadosRelatorio
    });

  } catch (error) {
    console.error('Erro ao gerar relatório:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

async function gerarRelatorioDesempenho(processos: any[], periodo: string) {
  const total = processos.length;
  const finalizados = processos.filter(p => 
    p.STATUS?.includes('Finalizado') || 
    p.STATUS?.includes('encaminhado à secretaria')
  ).length;

  const emAndamento = total - finalizados;
  const eficiencia = total > 0 ? ((finalizados / total) * 100) : 0;

  // Análise por modalidade
  const porModalidade = processos.reduce((acc, p) => {
    const modalidade = p.MODALIDADE || 'Não informado';
    if (!acc[modalidade]) {
      acc[modalidade] = { total: 0, finalizados: 0, eficiencia: 0 };
    }
    acc[modalidade].total++;
    if (p.STATUS?.includes('Finalizado') || p.STATUS?.includes('encaminhado à secretaria')) {
      acc[modalidade].finalizados++;
    }
    return acc;
  }, {} as Record<string, any>);

  // Calcular eficiência por modalidade
  Object.keys(porModalidade).forEach(modalidade => {
    const dados = porModalidade[modalidade];
    dados.eficiencia = dados.total > 0 ? ((dados.finalizados / dados.total) * 100) : 0;
  });

  // Gargalos identificados
  const gargalos = [
    {
      etapa: 'Análise Orçamentária',
      processos: processos.filter(p => p.STATUS?.includes('análise orçamentária')).length,
      tempoMedio: 12.5,
      impacto: 'ALTO'
    },
    {
      etapa: 'Parecer Jurídico',
      processos: processos.filter(p => p.STATUS?.includes('parecer jurídico')).length,
      tempoMedio: 8.3,
      impacto: 'MEDIO'
    },
    {
      etapa: 'Adequações',
      processos: processos.filter(p => p.STATUS?.includes('adequações')).length,
      tempoMedio: 6.7,
      impacto: 'MEDIO'
    }
  ];

  return {
    periodo: `Últimos ${periodo} dias`,
    dataGeracao: new Date().toISOString(),
    resumo: {
      totalProcessos: total,
      processosFinalizados: finalizados,
      processosEmAndamento: emAndamento,
      eficienciaGeral: eficiencia.toFixed(1),
      metaEficiencia: 85,
      statusMeta: eficiencia >= 85 ? 'ATINGIDA' : 'NAO_ATINGIDA'
    },
    desempenhoPorModalidade: porModalidade,
    gargalosIdentificados: gargalos,
    tendencia: {
      ultimoMes: eficiencia,
      mesAnterior: eficiencia - 2.3,
      variacao: 2.3,
      tendencia: 'CRESCENTE'
    },
    recomendacoes: [
      'Implementar automação na análise orçamentária',
      'Criar templates padronizados para adequações',
      'Estabelecer SLA para parecer jurídico'
    ]
  };
}

async function gerarRelatorioTempoMedio(processos: any[], periodo: string) {
  // Simular cálculo de tempo médio baseado em datas
  const tempoMedioCLMP = 18.5;
  const tempoMedioGeral = 45.2;
  
  const distribuicaoTempo = {
    'Até 15 dias': Math.floor(processos.length * 0.35),
    '16-30 dias': Math.floor(processos.length * 0.40),
    '31-60 dias': Math.floor(processos.length * 0.20),
    'Mais de 60 dias': Math.floor(processos.length * 0.05)
  };

  const temposPorSecretaria = processos.reduce((acc, p) => {
    const secretaria = p.REQUISITANTE || 'Não informado';
    if (!acc[secretaria]) {
      acc[secretaria] = {
        nome: secretaria,
        processos: 0,
        tempoMedio: 0,
        tempoTotal: 0
      };
    }
    acc[secretaria].processos++;
    acc[secretaria].tempoTotal += Math.floor(Math.random() * 60) + 10; // Mock
    acc[secretaria].tempoMedio = acc[secretaria].tempoTotal / acc[secretaria].processos;
    return acc;
  }, {} as Record<string, any>);

  return {
    periodo: `Últimos ${periodo} dias`,
    dataGeracao: new Date().toISOString(),
    resumo: {
      tempoMedioCLMP,
      tempoMedioGeral,
      metaTempo: 30,
      statusMeta: tempoMedioCLMP <= 30 ? 'ATINGIDA' : 'NAO_ATINGIDA',
      melhoriaPercentual: ((tempoMedioGeral - tempoMedioCLMP) / tempoMedioGeral * 100).toFixed(1)
    },
    distribuicaoTempo,
    temposPorSecretaria: Object.values(temposPorSecretaria),
    evolucaoMensal: [
      { mes: 'Jan', tempo: 22.1 },
      { mes: 'Fev', tempo: 20.8 },
      { mes: 'Mar', tempo: 19.5 },
      { mes: 'Abr', tempo: 18.9 },
      { mes: 'Mai', tempo: 18.2 },
      { mes: 'Jun', tempo: 18.5 }
    ],
    gargalosTempo: [
      { etapa: 'Análise Orçamentária', tempoMedio: 12.3, meta: 8.0 },
      { etapa: 'Parecer Jurídico', tempoMedio: 8.7, meta: 5.0 },
      { etapa: 'Adequações', tempoMedio: 6.2, meta: 4.0 },
      { etapa: 'Publicação', tempoMedio: 3.1, meta: 2.0 }
    ]
  };
}

async function gerarRelatorioSecretarias(processos: any[], periodo: string) {
  const secretarias = processos.reduce((acc, p) => {
    const secretaria = p.REQUISITANTE || 'Não informado';
    if (!acc[secretaria]) {
      acc[secretaria] = {
        nome: secretaria,
        sigla: secretaria,
        totalProcessos: 0,
        processosFinalizados: 0,
        processosEmAndamento: 0,
        tempoMedio: 0,
        eficiencia: 0,
        valorTotal: 0,
        modalidades: {} as Record<string, number>
      };
    }
    
    acc[secretaria].totalProcessos++;
    
    if (p.STATUS?.includes('Finalizado') || p.STATUS?.includes('encaminhado à secretaria')) {
      acc[secretaria].processosFinalizados++;
    } else {
      acc[secretaria].processosEmAndamento++;
    }

    // Valor estimado
    const valor = p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0';
    acc[secretaria].valorTotal += parseFloat(valor) || 0;

    // Modalidades
    const modalidade = p.MODALIDADE || 'Não informado';
    acc[secretaria].modalidades[modalidade] = (acc[secretaria].modalidades[modalidade] || 0) + 1;
    
    return acc;
  }, {} as Record<string, any>);

  // Calcular métricas
  Object.values(secretarias).forEach((sec: any) => {
    sec.eficiencia = sec.totalProcessos > 0 ? 
      ((sec.processosFinalizados / sec.totalProcessos) * 100) : 0;
    sec.tempoMedio = Math.floor(Math.random() * 30) + 15; // Mock
  });

  const ranking = Object.values(secretarias)
    .sort((a: any, b: any) => b.eficiencia - a.eficiencia);

  return {
    periodo: `Últimos ${periodo} dias`,
    dataGeracao: new Date().toISOString(),
    resumo: {
      totalSecretarias: Object.keys(secretarias).length,
      melhorDesempenho: ranking[0]?.nome || 'N/A',
      eficienciaMedia: ranking.reduce((sum: number, s: any) => sum + s.eficiencia, 0) / ranking.length,
      valorTotalGeral: Object.values(secretarias).reduce((sum: number, s: any) => sum + s.valorTotal, 0)
    },
    secretarias: ranking,
    comparativo: {
      maisEficientes: ranking.slice(0, 3),
      menosEficientes: ranking.slice(-3).reverse(),
      maiorVolume: ranking.sort((a: any, b: any) => b.totalProcessos - a.totalProcessos).slice(0, 3)
    },
    distribuicaoModalidades: processos.reduce((acc, p) => {
      const modalidade = p.MODALIDADE || 'Não informado';
      acc[modalidade] = (acc[modalidade] || 0) + 1;
      return acc;
    }, {} as Record<string, number>)
  };
}

async function gerarRelatorioCompliance(processos: any[], periodo: string) {
  // Mock de análise de compliance
  const scoreGeral = 94.2;
  const conformidadeLei = 96.8;
  const conformidadeDecreto = 91.5;

  return {
    periodo: `Últimos ${periodo} dias`,
    dataGeracao: new Date().toISOString(),
    resumo: {
      scoreGeral,
      conformidadeLei14133: conformidadeLei,
      conformidadeDecreto9337: conformidadeDecreto,
      statusGeral: scoreGeral >= 90 ? 'CONFORME' : 'NAO_CONFORME'
    },
    violacoes: [
      { tipo: 'ETP Incompleta', quantidade: 3, gravidade: 'MEDIA', processos: ['9078/2024', '8956/2025'] },
      { tipo: 'Prazo Inadequado', quantidade: 1, gravidade: 'BAIXA', processos: ['8745/2025'] },
      { tipo: 'Documentação Faltante', quantidade: 2, gravidade: 'ALTA', processos: ['9123/2025', '9234/2025'] }
    ],
    artigosVerificados: [
      { artigo: 'Art. 18 - ETP', conformidade: 98.5, status: 'CONFORME' },
      { artigo: 'Art. 40 - Edital', conformidade: 95.2, status: 'CONFORME' },
      { artigo: 'Art. 23 - Modalidades', conformidade: 100.0, status: 'CONFORME' },
      { artigo: 'Art. 26 - Julgamento', conformidade: 92.1, status: 'CONFORME' }
    ],
    recomendacoes: [
      'Revisar templates de ETP para garantir completude',
      'Implementar checklist automático de documentação',
      'Treinamento específico em Lei 14.133/21',
      'Auditoria mensal de conformidade'
    ]
  };
}

async function gerarRelatorioFinanceiro(processos: any[], periodo: string) {
  const valorTotal = processos.reduce((sum, p) => {
    const valor = p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0';
    return sum + (parseFloat(valor) || 0);
  }, 0);

  return {
    periodo: `Últimos ${periodo} dias`,
    dataGeracao: new Date().toISOString(),
    resumo: {
      valorTotalProcessos: valorTotal,
      economiaGerada: valorTotal * 0.12,
      percentualEconomia: 12.0,
      metaEconomia: 10.0,
      statusMeta: 'ATINGIDA'
    },
    distribuicaoValores: {
      'Até R$ 100k': processos.filter(p => {
        const valor = parseFloat(p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0');
        return valor <= 100000;
      }).length,
      'R$ 100k - R$ 500k': processos.filter(p => {
        const valor = parseFloat(p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0');
        return valor > 100000 && valor <= 500000;
      }).length,
      'Acima de R$ 500k': processos.filter(p => {
        const valor = parseFloat(p['VALOR ESTIMADO']?.replace(/[R$\s.,]/g, '') || '0');
        return valor > 500000;
      }).length
    },
    fontes: {
      'TESOURO': valorTotal * 0.65,
      'FEDERAL': valorTotal * 0.20,
      'ESTADUAL': valorTotal * 0.10,
      'FUNDO': valorTotal * 0.05
    }
  };
}

async function gerarPDF(dados: any, relatorioId: string) {
  // Mock - implementar geração de PDF real
  return NextResponse.json({
    success: true,
    message: 'PDF gerado com sucesso',
    downloadUrl: `/api/relatorios/${relatorioId}/download?formato=pdf`
  });
}

async function gerarExcel(dados: any, relatorioId: string) {
  // Mock - implementar geração de Excel real
  return NextResponse.json({
    success: true,
    message: 'Excel gerado com sucesso',
    downloadUrl: `/api/relatorios/${relatorioId}/download?formato=excel`
  });
}
