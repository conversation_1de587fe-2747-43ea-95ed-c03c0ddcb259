import { NextRequest, NextResponse } from 'next/server';

interface Contrato {
  id: string;
  numero: string;
  objeto: string;
  contratada: string;
  valor: number;
  dataInicio: string;
  dataVencimento: string;
  status: 'VIGENTE' | 'VENCIDO' | 'SUSPENSO' | 'ENCERRADO';
  diasRestantes: number;
  secretaria: string;
  modalidade: string;
  processoOrigem: string;
  cnpj?: string;
  responsavel?: string;
  observacoes?: string;
}

// Mock data - Período: 01/05/25 até 06/06/25
const mockContratos: Contrato[] = [
  {
    id: '1',
    numero: '013/2025',
    objeto: 'Fornecimento de medicamentos básicos para unidades de saúde',
    contratada: 'Farmácia Central Ltda',
    valor: 125000.00,
    dataInicio: '15/05/25',
    dataVencimento: '15/11/25',
    status: 'VIGENTE',
    diasRestantes: 163,
    secretaria: 'SESAU',
    modalidade: 'Pregão Eletrônico',
    processoOrigem: '9078/2025',
    cnpj: '12.345.678/0001-90',
    responsavel: '<PERSON>',
    observacoes: 'Contrato com renovação automática'
  },
  {
    id: '2',
    numero: '014/2025',
    objeto: 'Serviços de limpeza urbana e coleta de resíduos',
    contratada: 'Limpa Cidade S.A.',
    valor: 850000.00,
    dataInicio: '01/05/25',
    dataVencimento: '30/04/26',
    status: 'VIGENTE',
    diasRestantes: 328,
    secretaria: 'SEMAM',
    modalidade: 'Concorrência',
    processoOrigem: '8956/2025',
    cnpj: '98.765.432/0001-10',
    responsavel: 'João Santos',
    observacoes: 'Contrato de grande porte'
  },
  {
    id: '3',
    numero: '012/2025',
    objeto: 'Material de escritório e suprimentos administrativos',
    contratada: 'Papelaria Moderna Ltda',
    valor: 45000.00,
    dataInicio: '10/04/25',
    dataVencimento: '09/05/25',
    status: 'VENCIDO',
    diasRestantes: -28,
    secretaria: 'SSDAN',
    modalidade: 'Pregão Presencial',
    processoOrigem: '8745/2025',
    cnpj: '11.222.333/0001-44',
    responsavel: 'Ana Costa',
    observacoes: 'Contrato vencido - necessária renovação'
  },
  {
    id: '4',
    numero: '015/2025',
    objeto: 'Serviços de manutenção predial',
    contratada: 'Construtora Boa Vista',
    valor: 180000.00,
    dataInicio: '20/05/25',
    dataVencimento: '19/05/26',
    status: 'VIGENTE',
    diasRestantes: 347,
    secretaria: 'SSDAN',
    modalidade: 'Tomada de Preços',
    processoOrigem: '9123/2025',
    cnpj: '55.666.777/0001-88',
    responsavel: 'Carlos Oliveira',
    observacoes: 'Inclui manutenção preventiva e corretiva'
  },
  {
    id: '5',
    numero: '016/2025',
    objeto: 'Fornecimento de combustível para frota municipal',
    contratada: 'Posto Central Combustíveis',
    valor: 320000.00,
    dataInicio: '01/06/25',
    dataVencimento: '31/05/26',
    status: 'VIGENTE',
    diasRestantes: 359,
    secretaria: 'SEMAM',
    modalidade: 'Pregão Eletrônico',
    processoOrigem: '9234/2025',
    cnpj: '77.888.999/0001-22',
    responsavel: 'Pedro Lima',
    observacoes: 'Contrato com sistema de abastecimento eletrônico'
  }
];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const status = searchParams.get('status') || '';
    const secretaria = searchParams.get('secretaria') || '';

    // Filtrar contratos
    let filteredContratos = mockContratos;

    if (search) {
      filteredContratos = filteredContratos.filter(contrato =>
        contrato.numero.toLowerCase().includes(search.toLowerCase()) ||
        contrato.objeto.toLowerCase().includes(search.toLowerCase()) ||
        contrato.contratada.toLowerCase().includes(search.toLowerCase()) ||
        contrato.processoOrigem.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (status) {
      filteredContratos = filteredContratos.filter(contrato => contrato.status === status);
    }

    if (secretaria) {
      filteredContratos = filteredContratos.filter(contrato => 
        contrato.secretaria.toLowerCase().includes(secretaria.toLowerCase())
      );
    }

    // Paginação
    const offset = (page - 1) * limit;
    const totalContratos = filteredContratos.length;
    const contratosPaginados = filteredContratos.slice(offset, offset + limit);

    // Calcular estatísticas
    const stats = {
      total: mockContratos.length,
      vigentes: mockContratos.filter(c => c.status === 'VIGENTE').length,
      vencidos: mockContratos.filter(c => c.status === 'VENCIDO').length,
      suspensos: mockContratos.filter(c => c.status === 'SUSPENSO').length,
      encerrados: mockContratos.filter(c => c.status === 'ENCERRADO').length,
      vencendoEm30Dias: mockContratos.filter(c => c.status === 'VIGENTE' && c.diasRestantes <= 30).length,
      valorTotal: mockContratos.reduce((sum, c) => sum + c.valor, 0),
      valorVigentes: mockContratos.filter(c => c.status === 'VIGENTE').reduce((sum, c) => sum + c.valor, 0)
    };

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalContratos / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        contratos: contratosPaginados,
        stats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalContratos,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error('Erro na API de contratos:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Validação básica
    const requiredFields = ['numero', 'objeto', 'contratada', 'valor', 'dataInicio', 'dataVencimento', 'secretaria'];
    for (const field of requiredFields) {
      if (!body[field]) {
        return NextResponse.json(
          { success: false, error: `Campo obrigatório: ${field}` },
          { status: 400 }
        );
      }
    }

    // Simular criação de contrato
    const novoContrato: Contrato = {
      id: (mockContratos.length + 1).toString(),
      numero: body.numero,
      objeto: body.objeto,
      contratada: body.contratada,
      valor: parseFloat(body.valor),
      dataInicio: body.dataInicio,
      dataVencimento: body.dataVencimento,
      status: body.status || 'VIGENTE',
      diasRestantes: 0, // Calcular baseado nas datas
      secretaria: body.secretaria,
      modalidade: body.modalidade || 'Pregão Eletrônico',
      processoOrigem: body.processoOrigem || '',
      cnpj: body.cnpj,
      responsavel: body.responsavel,
      observacoes: body.observacoes
    };

    // TODO: Salvar no banco de dados real
    mockContratos.push(novoContrato);

    return NextResponse.json({
      success: true,
      message: 'Contrato criado com sucesso',
      data: novoContrato
    });
  } catch (error) {
    console.error('Erro ao criar contrato:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
