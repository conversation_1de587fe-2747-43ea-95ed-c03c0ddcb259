@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96%;
    --secondary-foreground: 222.2 84% 4.9%;
    --muted: 210 40% 96%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 210 40% 96%;
    --accent-foreground: 222.2 84% 4.9%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 222.2 84% 4.9%;
    --foreground: 210 40% 98%;
    --card: 222.2 84% 4.9%;
    --card-foreground: 210 40% 98%;
    --popover: 222.2 84% 4.9%;
    --popover-foreground: 210 40% 98%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 84% 4.9%;
    --secondary: 217.2 32.6% 17.5%;
    --secondary-foreground: 210 40% 98%;
    --muted: 217.2 32.6% 17.5%;
    --muted-foreground: 215 20.2% 65.1%;
    --accent: 217.2 32.6% 17.5%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 217.2 32.6% 25%;
    --input: 217.2 32.6% 25%;
    --ring: 224.3 76.3% 94.1%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

/* Scrollbar personalizada */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  @apply bg-muted;
}

::-webkit-scrollbar-thumb {
  @apply bg-muted-foreground/30 rounded-full;
}

::-webkit-scrollbar-thumb:hover {
  @apply bg-muted-foreground/50;
}

/* Animações suaves */
.animate-fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Ocultar elementos indesejados que possam aparecer no canto inferior esquerdo */
div[style*="position: fixed"][style*="bottom: 0"][style*="left: 0"],
div[style*="position: absolute"][style*="bottom: 0"][style*="left: 0"],
div[style*="z-index"][style*="bottom"][style*="left"] {
  display: none !important;
}

/* Ocultar elementos de notificação mal posicionados */
.notification-overlay,
.toast-container,
[data-testid*="notification"],
[class*="notification"][style*="bottom"],
[class*="toast"][style*="bottom"] {
  display: none !important;
}

/* Ocultar elementos suspeitos que possam conter apenas "N" */
div:not([class]):not([id]):empty::before,
div:not([class]):not([id]):empty::after,
span:not([class]):not([id]):empty::before,
span:not([class]):not([id]):empty::after {
  display: none !important;
}

/* Ocultar elementos de desenvolvimento/debug */
[data-reactroot] > div:last-child:not([class]):not([id]),
body > div:last-child:not([class]):not([id]):not([data-reactroot]) {
  display: none !important;
}

/* Regras simplificadas para ocultar elementos problemáticos */

/* Força ocultar qualquer elemento suspeito no canto inferior esquerdo */
div[style*="position"][style*="left"][style*="bottom"],
span[style*="position"][style*="left"][style*="bottom"] {
  visibility: hidden !important;
  opacity: 0 !important;
  pointer-events: none !important;
}

/* CSS específico para ocultar o "N" problemático */
body::after,
body::before,
html::after,
html::before {
  content: none !important;
  display: none !important;
}

/* Ocultar elementos que possam estar sendo injetados */
[data-overlay],
[data-portal],
[data-floating],
[role="tooltip"],
[role="alert"]:not(.alert):not(.notification) {
  display: none !important;
}

/* Regras simples para ocultar o "N" problemático */
div:empty {
  display: none !important;
}

span:empty {
  display: none !important;
}

