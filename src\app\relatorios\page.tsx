'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
// import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  BarChart3,
  Download,
  Calendar,
  Filter,
  FileText,
  Clock,
  DollarSign,
  Users,
  Building2,
  Activity,
  AlertTriangle,
  CheckCircle,
  Eye,
  Share,
  Search,
  TrendingUp,
  RefreshCw,
  ExternalLink
} from 'lucide-react';

export default function RelatoriosPage() {
  const [filtroTipo, setFiltroTipo] = useState<string>('todos');
  const [filtroCategoria, setFiltroCategoria] = useState<string>('todos');
  const [filtroStatus, setFiltroStatus] = useState<string>('todos');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [relatorios, setRelatorios] = useState<any[]>([]);
  const [stats, setStats] = useState<any>({});
  const [loading, setLoading] = useState(true);
  const [selectedRelatorio, setSelectedRelatorio] = useState<any>(null);
  const [showDetalhes, setShowDetalhes] = useState(false);

  // Carregar dados dos relatórios
  useEffect(() => {
    carregarRelatorios();
  }, [filtroTipo, filtroCategoria, filtroStatus]);

  const carregarRelatorios = async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams();
      if (filtroTipo !== 'todos') params.append('tipo', filtroTipo);
      if (filtroCategoria !== 'todos') params.append('categoria', filtroCategoria);

      const response = await fetch(`/api/relatorios?${params.toString()}`);
      const data = await response.json();

      if (data.success) {
        setRelatorios(data.data.relatorios);
        setStats(data.data.stats);
      }
    } catch (error) {
      console.error('Erro ao carregar relatórios:', error);
    } finally {
      setLoading(false);
    }
  };

  const visualizarRelatorio = async (relatorioId: string) => {
    try {
      const response = await fetch(`/api/relatorios/${relatorioId}`);
      const data = await response.json();

      if (data.success) {
        setSelectedRelatorio(data.data);
        setShowDetalhes(true);
      }
    } catch (error) {
      console.error('Erro ao carregar relatório:', error);
    }
  };

  const exportarRelatorio = async (relatorioId: string, formato: 'pdf' | 'excel') => {
    try {
      const response = await fetch(`/api/relatorios/${relatorioId}?formato=${formato}`);
      const data = await response.json();

      if (data.success && data.downloadUrl) {
        window.open(data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Erro ao exportar relatório:', error);
    }
  };

  const mockRelatorios = [
    {
      id: '1',
      titulo: 'Dashboard Executivo',
      descricao: 'Visão geral de todos os processos, contratos e métricas de performance',
      tipo: 'performance',
      categoria: 'estrategico',
      status: 'atualizado',
      visualizacoes: 245,
      downloads: 89,
      icone: BarChart3,
      cor: 'blue'
    },
    {
      id: '2',
      titulo: 'Relatório de Processos por Secretaria',
      descricao: 'Análise detalhada do desempenho de cada secretaria',
      tipo: 'processos',
      categoria: 'gerencial',
      status: 'atualizado',
      visualizacoes: 156,
      downloads: 67,
      icone: Building2,
      cor: 'green'
    },
    {
      id: '3',
      titulo: 'Análise de Tempo de Processamento',
      descricao: 'Métricas de eficiência e gargalos no fluxo',
      tipo: 'performance',
      categoria: 'operacional',
      status: 'atualizado',
      visualizacoes: 98,
      downloads: 45,
      icone: Clock,
      cor: 'orange'
    },
    {
      id: '4',
      titulo: 'Relatório de Compliance',
      descricao: 'Conformidade com Lei 14.133/21 e Decreto Municipal',
      tipo: 'compliance',
      categoria: 'estrategico',
      status: 'pendente',
      visualizacoes: 134,
      downloads: 78,
      icone: AlertTriangle,
      cor: 'red'
    },
    {
      id: '5',
      titulo: 'Análise Financeira de Contratos',
      descricao: 'Economia gerada e performance de fornecedores',
      tipo: 'financeiro',
      categoria: 'gerencial',
      status: 'processando',
      visualizacoes: 89,
      downloads: 34,
      icone: DollarSign,
      cor: 'purple'
    },
    {
      id: '6',
      titulo: 'Relatório de Usuários e Acessos',
      descricao: 'Atividade dos usuários e logs de segurança',
      tipo: 'performance',
      categoria: 'operacional',
      status: 'atualizado',
      visualizacoes: 67,
      downloads: 23,
      icone: Users,
      cor: 'indigo'
    }
  ];

  // Usar dados reais ou mock como fallback
  const dadosRelatorios = relatorios.length > 0 ? relatorios : mockRelatorios;

  const relatoriosFiltrados = dadosRelatorios.filter(relatorio => {
    const matchTipo = filtroTipo === 'todos' || relatorio.tipo === filtroTipo;
    const matchCategoria = filtroCategoria === 'todos' || relatorio.categoria === filtroCategoria;
    const matchStatus = filtroStatus === 'todos' || relatorio.status === filtroStatus;
    const matchSearch = !searchTerm ||
      relatorio.titulo.toLowerCase().includes(searchTerm.toLowerCase()) ||
      relatorio.descricao.toLowerCase().includes(searchTerm.toLowerCase());

    return matchTipo && matchCategoria && matchStatus && matchSearch;
  });

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'atualizado': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'pendente': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'processando': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCategoriaColor = (categoria: string) => {
    switch (categoria) {
      case 'estrategico': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'gerencial': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'operacional': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getCorIcon = (cor: string) => {
    const cores: { [key: string]: string } = {
      blue: 'text-blue-600',
      green: 'text-green-600',
      orange: 'text-orange-600',
      red: 'text-red-600',
      purple: 'text-purple-600',
      indigo: 'text-indigo-600'
    };
    return cores[cor] || 'text-gray-600';
  };

  // Usar stats da API ou calcular dos dados mock
  const estatisticas = stats.total ? stats : {
    total: dadosRelatorios.length,
    atualizados: dadosRelatorios.filter(r => r.status === 'atualizado').length,
    pendentes: dadosRelatorios.filter(r => r.status === 'pendente').length,
    processando: dadosRelatorios.filter(r => r.status === 'processando').length,
    totalVisualizacoes: dadosRelatorios.reduce((acc, r) => acc + r.visualizacoes, 0),
    totalDownloads: dadosRelatorios.reduce((acc, r) => acc + r.downloads, 0)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Central de Relatórios</h1>
          <p className="text-muted-foreground mt-2">
            Analytics avançados e relatórios gerenciais para tomada de decisão
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-xs">
            <BarChart3 className="mr-1 h-3 w-3" />
            {relatoriosFiltrados.length} relatórios
          </Badge>

          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar Todos
          </Button>

          <Button>
            <FileText className="mr-2 h-4 w-4" />
            Novo Relatório
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{estatisticas.total}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Atualizados</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.atualizados}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pendentes</p>
                <p className="text-2xl font-bold text-yellow-600">{estatisticas.pendentes}</p>
              </div>
              <Clock className="h-8 w-8 text-yellow-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Processando</p>
                <p className="text-2xl font-bold text-blue-600">{estatisticas.processando}</p>
              </div>
              <Activity className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Visualizações</p>
                <p className="text-2xl font-bold text-purple-600">{estatisticas.totalVisualizacoes}</p>
              </div>
              <Eye className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Downloads</p>
                <p className="text-2xl font-bold text-indigo-600">{estatisticas.totalDownloads}</p>
              </div>
              <Download className="h-8 w-8 text-indigo-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros e Pesquisa */}
      <Card>
        <CardContent className="p-6">
          <div className="space-y-4">
            {/* Barra de Pesquisa */}
            <div className="flex items-center space-x-4">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Pesquisar relatórios..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10"
                />
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={carregarRelatorios}
                disabled={loading}
              >
                {loading ? (
                  <RefreshCw className="h-4 w-4 animate-spin" />
                ) : (
                  <RefreshCw className="h-4 w-4" />
                )}
                Atualizar
              </Button>
            </div>

            {/* Filtros */}
            <div className="flex flex-col sm:flex-row gap-4">
              <select
                value={filtroTipo}
                onChange={(e) => setFiltroTipo(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="todos">Todos os Tipos</option>
                <option value="processos">Processos</option>
                <option value="performance">Performance</option>
                <option value="financeiro">Financeiro</option>
                <option value="compliance">Compliance</option>
                <option value="operacional">Operacional</option>
              </select>

              <select
                value={filtroCategoria}
                onChange={(e) => setFiltroCategoria(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="todos">Todas as Categorias</option>
                <option value="estrategico">Estratégico</option>
                <option value="gerencial">Gerencial</option>
                <option value="operacional">Operacional</option>
              </select>

              <select
                value={filtroStatus}
                onChange={(e) => setFiltroStatus(e.target.value)}
                className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
              >
                <option value="todos">Todos os Status</option>
                <option value="atualizado">Atualizados</option>
                <option value="pendente">Pendentes</option>
                <option value="processando">Processando</option>
              </select>

              <Button variant="outline" className="flex items-center">
                <Filter className="mr-2 h-4 w-4" />
                Filtros Avançados
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Grid de Relatórios */}
      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {relatoriosFiltrados.map((relatorio) => {
          const IconComponent = relatorio.icone;
          return (
            <Card key={relatorio.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center space-x-3">
                    <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
                      <IconComponent className={`h-6 w-6 ${getCorIcon(relatorio.cor)}`} />
                    </div>
                    <div className="flex-1">
                      <h3 className="font-semibold text-foreground">{relatorio.titulo}</h3>
                      <p className="text-sm text-muted-foreground mt-1">{relatorio.descricao}</p>
                    </div>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge className={getStatusColor(relatorio.status)}>
                    {relatorio.status}
                  </Badge>
                  <Badge className={getCategoriaColor(relatorio.categoria)}>
                    {relatorio.categoria}
                  </Badge>
                </div>

                <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border mb-4">
                  <div className="text-center">
                    <p className="text-lg font-bold text-purple-600">{relatorio.visualizacoes}</p>
                    <p className="text-xs text-muted-foreground">Visualizações</p>
                  </div>
                  <div className="text-center">
                    <p className="text-lg font-bold text-indigo-600">{relatorio.downloads}</p>
                    <p className="text-xs text-muted-foreground">Downloads</p>
                  </div>
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center space-x-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => visualizarRelatorio(relatorio.id)}
                      title="Visualizar Relatório"
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => exportarRelatorio(relatorio.id, 'pdf')}
                      title="Download PDF"
                    >
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => exportarRelatorio(relatorio.id, 'excel')}
                      title="Download Excel"
                    >
                      <FileText className="h-4 w-4" />
                    </Button>
                  </div>

                  <Button
                    size="sm"
                    onClick={() => visualizarRelatorio(relatorio.id)}
                  >
                    <ExternalLink className="mr-2 h-4 w-4" />
                    Visualizar
                  </Button>
                </div>
              </CardContent>
            </Card>
          );
        })}
      </div>

      {relatoriosFiltrados.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <BarChart3 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhum relatório encontrado
            </h3>
            <p className="text-muted-foreground mb-4">
              Tente ajustar os filtros para encontrar relatórios.
            </p>
          </CardContent>
        </Card>
      )}

      {/* Modal de Detalhes do Relatório */}
      {showDetalhes && selectedRelatorio && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-background rounded-lg shadow-xl max-w-4xl w-full max-h-[90vh] overflow-y-auto">
            <CardHeader className="border-b">
              <div className="flex items-center justify-between">
                <CardTitle className="text-xl">
                  Detalhes do Relatório
                </CardTitle>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowDetalhes(false)}
                >
                  ✕
                </Button>
              </div>
            </CardHeader>
            <CardContent className="p-6">
              <div className="space-y-6">
                {/* Resumo */}
                {selectedRelatorio.resumo && (
                  <div>
                    <h3 className="text-lg font-semibold mb-3">Resumo Executivo</h3>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                      {Object.entries(selectedRelatorio.resumo).map(([key, value]) => (
                        <div key={key} className="bg-gray-50 dark:bg-gray-800 p-3 rounded">
                          <p className="text-sm text-muted-foreground capitalize">
                            {key.replace(/([A-Z])/g, ' $1').toLowerCase()}
                          </p>
                          <p className="font-semibold">{String(value)}</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}

                {/* Dados específicos do relatório */}
                <div>
                  <h3 className="text-lg font-semibold mb-3">Dados Detalhados</h3>
                  <div className="bg-gray-50 dark:bg-gray-800 p-4 rounded">
                    <pre className="text-sm overflow-x-auto">
                      {JSON.stringify(selectedRelatorio, null, 2)}
                    </pre>
                  </div>
                </div>

                {/* Ações */}
                <div className="flex justify-end space-x-2 pt-4 border-t">
                  <Button
                    variant="outline"
                    onClick={() => exportarRelatorio(selectedRelatorio.id || 'dashboard-executivo', 'pdf')}
                  >
                    <Download className="mr-2 h-4 w-4" />
                    Baixar PDF
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => exportarRelatorio(selectedRelatorio.id || 'dashboard-executivo', 'excel')}
                  >
                    <FileText className="mr-2 h-4 w-4" />
                    Baixar Excel
                  </Button>
                  <Button onClick={() => setShowDetalhes(false)}>
                    Fechar
                  </Button>
                </div>
              </div>
            </CardContent>
          </div>
        </div>
      )}
    </div>
  );
}
