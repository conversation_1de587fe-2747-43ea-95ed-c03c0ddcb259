'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  BarChart3, 
  TrendingUp, 
  TrendingDown, 
  Clock, 
  CheckCircle, 
  AlertTriangle,
  Download,
  RefreshCw,
  Target
} from 'lucide-react';

export default function RelatorioDesempenhoPage() {
  const [dados, setDados] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [periodo, setPeriodo] = useState('30');

  useEffect(() => {
    carregarDados();
  }, [periodo]);

  const carregarDados = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/relatorios/desempenho?periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success) {
        setDados(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportarRelatorio = async (formato: 'pdf' | 'excel') => {
    try {
      const response = await fetch(`/api/relatorios/desempenho?formato=${formato}&periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success && data.downloadUrl) {
        window.open(data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Erro ao exportar:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!dados) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12 text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Erro ao carregar dados</h3>
            <p className="text-muted-foreground mb-4">
              Não foi possível carregar os dados do relatório.
            </p>
            <Button onClick={carregarDados}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Relatório de Desempenho</h1>
          <p className="text-muted-foreground mt-2">
            Análise de performance e eficiência dos processos - {dados.periodo}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={periodo}
            onChange={(e) => setPeriodo(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
          >
            <option value="7">Últimos 7 dias</option>
            <option value="30">Últimos 30 dias</option>
            <option value="90">Últimos 90 dias</option>
            <option value="365">Último ano</option>
          </select>

          <Button variant="outline" onClick={() => exportarRelatorio('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>

          <Button variant="outline" onClick={() => exportarRelatorio('excel')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>

          <Button onClick={carregarDados}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Resumo Executivo */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total de Processos</p>
                <p className="text-3xl font-bold">{dados.resumo.totalProcessos}</p>
              </div>
              <BarChart3 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Finalizados</p>
                <p className="text-3xl font-bold text-green-600">{dados.resumo.processosFinalizados}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Em Andamento</p>
                <p className="text-3xl font-bold text-orange-600">{dados.resumo.processosEmAndamento}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Eficiência</p>
                <p className="text-3xl font-bold text-purple-600">{dados.resumo.eficienciaGeral}%</p>
                <div className="flex items-center mt-1">
                  <Badge 
                    variant={dados.resumo.statusMeta === 'ATINGIDA' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    Meta: {dados.resumo.metaEficiencia}%
                  </Badge>
                </div>
              </div>
              <Target className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Desempenho por Modalidade */}
      <Card>
        <CardHeader>
          <CardTitle>Desempenho por Modalidade</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Object.entries(dados.desempenhoPorModalidade).map(([modalidade, dados]: [string, any]) => (
              <div key={modalidade} className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{modalidade}</h4>
                  <p className="text-sm text-muted-foreground">
                    {dados.finalizados} de {dados.total} processos finalizados
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold">{dados.eficiencia.toFixed(1)}%</p>
                  <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${dados.eficiencia}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Gargalos Identificados */}
      <Card>
        <CardHeader>
          <CardTitle>Gargalos Identificados</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dados.gargalosIdentificados.map((gargalo: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex items-center space-x-3">
                  <div className={`p-2 rounded-lg ${
                    gargalo.impacto === 'ALTO' ? 'bg-red-100 text-red-600' :
                    gargalo.impacto === 'MEDIO' ? 'bg-yellow-100 text-yellow-600' :
                    'bg-green-100 text-green-600'
                  }`}>
                    <AlertTriangle className="h-4 w-4" />
                  </div>
                  <div>
                    <h4 className="font-medium">{gargalo.etapa}</h4>
                    <p className="text-sm text-muted-foreground">
                      {gargalo.processos} processos afetados
                    </p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="font-medium">{gargalo.tempoMedio} dias</p>
                  <Badge variant={
                    gargalo.impacto === 'ALTO' ? 'destructive' :
                    gargalo.impacto === 'MEDIO' ? 'secondary' : 'default'
                  }>
                    {gargalo.impacto}
                  </Badge>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tendência e Recomendações */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Tendência de Performance</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <span>Mês Atual</span>
                <span className="font-bold">{dados.tendencia.ultimoMes}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Mês Anterior</span>
                <span className="font-bold">{dados.tendencia.mesAnterior}%</span>
              </div>
              <div className="flex items-center justify-between">
                <span>Variação</span>
                <div className="flex items-center space-x-2">
                  {dados.tendencia.tendencia === 'CRESCENTE' ? (
                    <TrendingUp className="h-4 w-4 text-green-600" />
                  ) : (
                    <TrendingDown className="h-4 w-4 text-red-600" />
                  )}
                  <span className={`font-bold ${
                    dados.tendencia.tendencia === 'CRESCENTE' ? 'text-green-600' : 'text-red-600'
                  }`}>
                    {dados.tendencia.variacao > 0 ? '+' : ''}{dados.tendencia.variacao}%
                  </span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recomendações</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {dados.recomendacoes.map((recomendacao: string, index: number) => (
                <div key={index} className="flex items-start space-x-3">
                  <div className="w-2 h-2 bg-blue-600 rounded-full mt-2 flex-shrink-0"></div>
                  <p className="text-sm">{recomendacao}</p>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
