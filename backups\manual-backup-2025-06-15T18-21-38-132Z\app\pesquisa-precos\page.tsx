'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Filter,
  Download,
  ExternalLink,
  TrendingUp,
  Calendar,
  MapPin,
  Building,
  DollarSign,
  Clock,
  FileText,
  History,
  Eye,
  CheckCircle,
  AlertCircle
} from 'lucide-react';

// Dados reais serão carregados da API
const mockHistoricoInicial: any[] = [];

export default function PesquisaPrecosPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [numeroProcesso, setNumeroProcesso] = useState('');
  const [loading, setLoading] = useState(false);
  const [loadingThema, setLoadingThema] = useState(false);
  const [activeTab, setActiveTab] = useState<'manual' | 'tr' | 'thema'>('manual');

  // Inicializar histórico com dados mockados
  const [historicoPesquisas, setHistoricoPesquisas] = useState<any[]>(mockHistoricoInicial);
  const [showHistorico, setShowHistorico] = useState(false);

  // Dados reais serão carregados da API PNCP
  const mockResults: any[] = [];

  const [results, setResults] = useState<any[]>([]);
  const [stats, setStats] = useState<any>({});
  const [themaItems, setThemaItems] = useState<any[]>([]);
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [mapaPreco, setMapaPreco] = useState<any>(null);
  const [showDespacho, setShowDespacho] = useState(false);
  const [showInsercaoManual, setShowInsercaoManual] = useState(false);
  const [ampliarPesquisa, setAmpliarPesquisa] = useState(false);
  const [pesquisaConcluida, setPesquisaConcluida] = useState(false);
  const [showCotacoesBanner, setShowCotacoesBanner] = useState(false);
  const [cotacoesTexto, setCotacoesTexto] = useState('');
  const [showAprovacaoExtracao, setShowAprovacaoExtracao] = useState(false);
  const [dadosExtraidos, setDadosExtraidos] = useState<any[]>([]);

  // Estados para busca manual completa
  const [dadosItem, setDadosItem] = useState({
    numeroProcesso: '',
    objeto: '',
    quantidade: '',
    unidade: '',
    volume: '',
    concentracao: '',
    tipo: '',
    especificacao: ''
  });

  // 🤖 ESTADO PARA IA DE PARSING
  const [textoParaParsing, setTextoParaParsing] = useState('');
  const [showParsingIA, setShowParsingIA] = useState(false);
  const [sugestoesUnidade, setSugestoesUnidade] = useState<string[]>([]);
  const [mostrarSugestoes, setMostrarSugestoes] = useState(false);

  const handleSearch = async () => {
    if (!searchTerm.trim()) return;

    setLoading(true);
    try {
      console.log('🔍 Iniciando busca REAL no PNCP:', searchTerm);

      // 🚀 BUSCA REAL NO PNCP
      const { PNCPApi } = await import('@/lib/pncpApi');

      const params = {
        termo: searchTerm.trim(),
        orgaoTipo: 'municipal' as const,
        dataInicio: '2024-01-01',
        dataFim: new Date().toISOString().split('T')[0]
      };

      console.log('📋 Parâmetros da busca:', params);

      const resultadoPNCP = await PNCPApi.buscarItens(params);

      console.log('✅ Resultado PNCP:', {
        total: resultadoPNCP.total,
        itens: resultadoPNCP.itens.length,
        media: resultadoPNCP.media
      });

      // 🔄 TRANSFORMAR DADOS PNCP PARA FORMATO DO SISTEMA
      const resultadosTransformados = resultadoPNCP.itens.map((item, index) => ({
        id: index + 1,
        item: item.descricao || searchTerm,
        categoria: 'Material/Serviço',
        precoMedio: item.preco || 0,
        menorPreco: item.preco * 0.9 || 0,
        maiorPreco: item.preco * 1.1 || 0,
        orgao: item.orgao || 'Órgão Público',
        dataLicitacao: item.dataContrato || new Date().toLocaleDateString('pt-BR'),
        modalidade: 'Pregão Eletrônico',
        status: 'Homologado',
        uf: 'SP',
        cidade: 'São Paulo',
        numeroLicitacao: item.numeroContrato || `${index + 1}/2025`,
        fornecedor: item.fornecedor || 'Fornecedor LTDA',
        unidade: item.unidade || 'UN',
        quantidade: 1,
        confiabilidade: item.relevancia || 85
      }));

      setResults(resultadosTransformados);
      setStats({
        totalResultados: resultadosTransformados.length,
        precoMedio: resultadoPNCP.media || 0,
        economiaEstimada: resultadosTransformados.length * 100
      });

      // Adicionar ao histórico
      const novaConsulta = {
        id: Date.now().toString(),
        termo: searchTerm,
        dataConsulta: new Date().toLocaleDateString('pt-BR'),
        resultados: resultadosTransformados.length,
        usuario: 'Marcos Isidoro',
        fonte: 'PNCP' as const,
        status: resultadosTransformados.length > 0 ? 'Sucesso' : 'Sem resultados'
      };
      setHistoricoPesquisas(prev => [novaConsulta, ...prev]);

      // Marcar pesquisa PNCP como concluída
      setPesquisaConcluida(true);

      // 📢 FEEDBACK INTELIGENTE PARA O USUÁRIO
      if (resultadosTransformados.length === 0) {
        console.warn('⚠️ Nenhum resultado encontrado para:', searchTerm);
        // Não mostrar alert - apenas deixar vazio
      } else {
        // 🧠 VERIFICAR SE SÃO SUGESTÕES OU RESULTADOS EXATOS
        const temSugestoes = resultadosTransformados.some(item => item.confiabilidade < 90);

        if (temSugestoes) {
          console.log(`💡 ${resultadosTransformados.length} sugestões encontradas para "${searchTerm}"`);
          // Adicionar flag para mostrar que são sugestões
          setStats(prev => ({
            ...prev,
            tipoResultado: 'sugestoes'
          }));
        } else {
          console.log(`✅ ${resultadosTransformados.length} resultados exatos encontrados!`);
          setStats(prev => ({
            ...prev,
            tipoResultado: 'exatos'
          }));
        }
      }

      setLoading(false);

    } catch (error) {
      console.error('❌ Erro na busca PNCP:', error);

      // 🔄 FALLBACK: Manter resultado vazio em caso de erro
      setResults([]);
      setStats({
        totalResultados: 0,
        precoMedio: 0,
        economiaEstimada: 0
      });

      // Adicionar ao histórico como erro
      const consultaErro = {
        id: Date.now().toString(),
        termo: searchTerm,
        dataConsulta: new Date().toLocaleDateString('pt-BR'),
        resultados: 0,
        usuario: 'Marcos Isidoro',
        fonte: 'PNCP' as const,
        status: 'Erro na consulta'
      };
      setHistoricoPesquisas(prev => [consultaErro, ...prev]);

      setLoading(false);
    }
  };

  // Dados reais serão carregados do sistema Thema
  const mockThemaItems: any[] = [];



  const handleBuscarThema = async () => {
    if (!numeroProcesso.trim()) return;

    setLoadingThema(true);
    try {
      // Simular busca no Thema
      setTimeout(() => {
        setThemaItems(mockThemaItems);
        setLoadingThema(false);
      }, 2000);

      // TODO: Implementar API real do Thema
      // const response = await fetch(`/api/thema/processo/${numeroProcesso}`);
    } catch (error) {
      console.error('Erro ao buscar no Thema:', error);
      setLoadingThema(false);
    }
  };

  const handlePesquisarItensSelecionados = async () => {
    if (selectedItems.length === 0) return;

    setLoading(true);
    try {
      // Simular pesquisa automática com IA no PNCP
      const itensSelecionados = themaItems.filter(item => selectedItems.includes(item.id));

      // Mock do resultado da pesquisa com IA
      const resultadosIA = itensSelecionados.map(item => ({
        itemOriginal: item,
        resultadosPNCP: [
          {
            id: `pncp_${item.id}_1`,
            item: item.descricao,
            categoria: item.categoria,
            precoMedio: item.valorUnitario * 0.9,
            menorPreco: item.valorUnitario * 0.8,
            maiorPreco: item.valorUnitario * 1.1,
            orgao: 'Prefeitura de São Paulo',
            dataLicitacao: '15/05/25',
            modalidade: 'Pregão Eletrônico',
            status: 'Homologado',
            uf: 'SP',
            cidade: 'São Paulo',
            confiabilidade: 95
          },
          {
            id: `pncp_${item.id}_2`,
            item: item.descricao,
            categoria: item.categoria,
            precoMedio: item.valorUnitario * 0.85,
            menorPreco: item.valorUnitario * 0.75,
            maiorPreco: item.valorUnitario * 1.05,
            orgao: 'Prefeitura de Campinas',
            dataLicitacao: '28/05/25',
            modalidade: 'Pregão Presencial',
            status: 'Vigente',
            uf: 'SP',
            cidade: 'Campinas',
            confiabilidade: 88
          }
        ],
        analiseIA: {
          mediaCalculada: item.valorUnitario * 0.875,
          medianaCalculada: item.valorUnitario * 0.85,
          precoRecomendado: item.valorUnitario * 0.85,
          economiaEstimada: (item.valorUnitario - (item.valorUnitario * 0.85)) * item.quantidade,
          confiabilidadeGeral: 91.5
        }
      }));

      setResults(resultadosIA);

      // Gerar mapa de preços
      const mapa = gerarMapaPrecos(resultadosIA);
      setMapaPreco(mapa);

    } catch (error) {
      console.error('Erro na pesquisa automática:', error);
    } finally {
      setLoading(false);
    }
  };

  const gerarMapaPrecos = (resultados: any[]) => {
    // 🔧 VERIFICAR SE SÃO RESULTADOS DO THEMA (com itemOriginal) OU MANUAIS
    const isThemaResults = resultados.length > 0 && resultados[0].itemOriginal;

    let valorOriginal = 0;
    let valorRecomendado = 0;

    if (isThemaResults) {
      // Resultados do Thema com análise IA
      valorOriginal = resultados.reduce((sum, r) => sum + r.itemOriginal.valorTotal, 0);
      valorRecomendado = resultados.reduce((sum, r) => sum + (r.analiseIA.precoRecomendado * r.itemOriginal.quantidade), 0);
    } else {
      // Resultados manuais/PNCP simples
      valorOriginal = resultados.reduce((sum, r) => sum + (r.precoMedio * r.quantidade), 0);
      valorRecomendado = resultados.reduce((sum, r) => sum + (r.menorPreco * r.quantidade), 0);
    }

    const economiaTotal = valorOriginal - valorRecomendado;

    // 🧠 GERAR ALERTAS DE UNIDADES PARA O MAPA
    const alertasUnidades: any[] = [];

    if (isThemaResults) {
      // Alertas para resultados do Thema
      resultados.forEach(resultado => {
        if (resultado.resultadosPNCP) {
          resultado.resultadosPNCP.forEach((pncp: any) => {
            // Simular detecção de unidades diferentes
            if (Math.random() > 0.7) { // 30% chance de ter alerta
              alertasUnidades.push({
                item: resultado.itemOriginal.descricao,
                fornecedor: pncp.orgao,
                unidadeDesejada: 'L',
                unidadeFornecedor: 'ml',
                conversao: '1L = 1000ml',
                observacao: '⚠️ Fornecedor vende em unidade menor'
              });
            }
          });
        }
      });
    } else {
      // Alertas para resultados manuais (simulação)
      resultados.forEach(resultado => {
        if (Math.random() > 0.8) { // 20% chance de ter alerta
          alertasUnidades.push({
            item: resultado.item,
            fornecedor: resultado.fornecedor,
            unidadeDesejada: resultado.unidade,
            unidadeFornecedor: resultado.unidade === 'L' ? 'ml' : 'UN',
            conversao: resultado.unidade === 'L' ? '1L = 1000ml' : '1UN = 1UN',
            observacao: '⚠️ Verificar especificação'
          });
        }
      });
    }

    return {
      numeroProcesso,
      dataGeracao: new Date().toLocaleDateString('pt-BR'),
      itens: resultados,
      alertasUnidades,
      resumo: {
        valorOriginal,
        valorRecomendado,
        economiaTotal,
        percentualEconomia: valorOriginal > 0 ? ((economiaTotal / valorOriginal) * 100).toFixed(2) : '0.00'
      }
    };
  };

  const gerarDespacho = () => {
    setShowDespacho(true);
  };

  // 🧠 UNIDADES INTELIGENTES - Baseado em experiência real
  const unidadesComuns = [
    'UN', 'CX', 'PCT', 'KG', 'L', 'ML', 'M', 'M²', 'M³',
    'RESMA', 'FRASCO', 'TUBO', 'AMPOLA', 'COMPRIMIDO',
    'CAIXA', 'PACOTE', 'LITRO', 'METRO', 'PEÇA', 'UNIDADE',
    'GALÃO', 'SACO', 'ROLO', 'FOLHA', 'ENVELOPE'
  ];

  // 🎯 DETECTAR UNIDADE BASEADA NO OBJETO (SEM ACENTOS)
  const detectarUnidadePorObjeto = (objeto: string): string[] => {
    // 🔤 NORMALIZAR TEXTO - Remove acentos e caracteres especiais
    const normalizar = (texto: string): string => {
      return texto
        .toLowerCase()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '') // Remove acentos
        .replace(/[ç]/g, 'c') // ç -> c
        .replace(/[^a-z0-9\s]/g, '') // Remove caracteres especiais
        .trim();
    };

    const objetoNormalizado = normalizar(objeto);

    // 🧪 ÁLCOOL E PRODUTOS DE LIMPEZA
    if (objetoNormalizado.includes('alcool') || objetoNormalizado.includes('gel') ||
        objetoNormalizado.includes('antisseptico') || objetoNormalizado.includes('sanitizante')) {
      return ['FRASCO', 'L', 'ML', 'UN'];
    }

    // 📄 PAPEL E MATERIAL DE ESCRITÓRIO
    if (objetoNormalizado.includes('papel') || objetoNormalizado.includes('folha') ||
        objetoNormalizado.includes('resma') || objetoNormalizado.includes('sulfite')) {
      return ['RESMA', 'FOLHA', 'PCT', 'CX'];
    }

    // 💊 MEDICAMENTOS E FARMÁCIA
    if (objetoNormalizado.includes('medicamento') || objetoNormalizado.includes('remedio') ||
        objetoNormalizado.includes('dipirona') || objetoNormalizado.includes('paracetamol') ||
        objetoNormalizado.includes('comprimido') || objetoNormalizado.includes('capsula')) {
      return ['CX', 'AMPOLA', 'COMPRIMIDO', 'FRASCO'];
    }

    // 💻 EQUIPAMENTOS DE INFORMÁTICA
    if (objetoNormalizado.includes('notebook') || objetoNormalizado.includes('computador') ||
        objetoNormalizado.includes('monitor') || objetoNormalizado.includes('impressora')) {
      return ['UN', 'PECA'];
    }

    // 🧽 PRODUTOS DE LIMPEZA
    if (objetoNormalizado.includes('limpeza') || objetoNormalizado.includes('detergente') ||
        objetoNormalizado.includes('desinfetante') || objetoNormalizado.includes('sabao')) {
      return ['L', 'ML', 'FRASCO', 'GALAO'];
    }

    // 🎨 TINTAS E VERNIZES
    if (objetoNormalizado.includes('tinta') || objetoNormalizado.includes('verniz') ||
        objetoNormalizado.includes('esmalte') || objetoNormalizado.includes('primer')) {
      return ['L', 'GALAO', 'LATA'];
    }

    // 🔌 CABOS E FIOS
    if (objetoNormalizado.includes('cabo') || objetoNormalizado.includes('fio') ||
        objetoNormalizado.includes('condutor')) {
      return ['M', 'ROLO', 'METRO'];
    }

    return ['UN', 'CX', 'PCT']; // Padrão
  };

  // 🤖 IA PARA PARSING DE TEXTO (TR, EMAIL, PLANILHA)
  const parsearTextoComIA = (texto: string) => {
    const normalizar = (str: string): string => {
      return str.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/[ç]/g, 'c');
    };

    const textoNorm = normalizar(texto);

    // 🎯 EXTRAIR DADOS ESTRUTURADOS
    const resultado = {
      objeto: '',
      quantidade: '',
      unidade: '',
      volume: '',
      concentracao: '',
      tipo: '',
      especificacao: texto // Manter texto original como backup
    };

    // 🧪 DETECTAR ÁLCOOL GEL
    if (textoNorm.includes('alcool') || textoNorm.includes('gel')) {
      resultado.objeto = 'álcool gel';
      resultado.tipo = 'gel';

      // Concentração
      const concMatch = texto.match(/(\d+)%|(\d+)\s*graus?|(\d+)°/i);
      if (concMatch) {
        resultado.concentracao = `${concMatch[1] || concMatch[2] || concMatch[3]}%`;
      }

      // Volume
      const volMatch = texto.match(/(\d+)\s*(ml|l|litros?)/i);
      if (volMatch) {
        resultado.volume = `${volMatch[1]}${volMatch[2].toUpperCase()}`;
      }

      // Quantidade
      const qtdMatch = texto.match(/(\d+)\s*(frascos?|unidades?|un)/i);
      if (qtdMatch) {
        resultado.quantidade = qtdMatch[1];
        resultado.unidade = 'FRASCO';
      }
    }

    // 📄 DETECTAR PAPEL
    else if (textoNorm.includes('papel')) {
      resultado.objeto = 'papel';

      // Tipo de papel
      if (textoNorm.includes('a4')) resultado.tipo = 'A4';
      if (textoNorm.includes('sulfite')) resultado.tipo = 'sulfite';

      // Gramatura
      const gramMatch = texto.match(/(\d+)\s*g/i);
      if (gramMatch) {
        resultado.concentracao = `${gramMatch[1]}g/m²`;
      }

      // Quantidade
      const qtdMatch = texto.match(/(\d+)\s*(resmas?|folhas?|pacotes?)/i);
      if (qtdMatch) {
        resultado.quantidade = qtdMatch[1];
        resultado.unidade = qtdMatch[2].includes('resma') ? 'RESMA' :
                           qtdMatch[2].includes('folha') ? 'FOLHA' : 'PCT';
      }
    }

    // 💊 DETECTAR MEDICAMENTOS
    else if (textoNorm.includes('dipirona') || textoNorm.includes('medicamento')) {
      resultado.objeto = textoNorm.includes('dipirona') ? 'dipirona' : 'medicamento';

      // Dosagem
      const doseMatch = texto.match(/(\d+)\s*mg/i);
      if (doseMatch) {
        resultado.concentracao = `${doseMatch[1]}mg`;
      }

      // Quantidade
      const qtdMatch = texto.match(/(\d+)\s*(caixas?|comprimidos?|ampolas?)/i);
      if (qtdMatch) {
        resultado.quantidade = qtdMatch[1];
        resultado.unidade = qtdMatch[2].includes('caixa') ? 'CX' :
                           qtdMatch[2].includes('comprimido') ? 'COMPRIMIDO' : 'AMPOLA';
      }
    }

    return resultado;
  };

  // 🧠 GERAR SUGESTÕES DE DESCRIÇÃO COMPLETA
  const gerarSugestoesDescricao = (objeto: string, quantidade: string, unidade: string): string[] => {
    if (!objeto || !quantidade || !unidade) return [];

    const normalizar = (texto: string): string => {
      return texto.toLowerCase().normalize('NFD').replace(/[\u0300-\u036f]/g, '').replace(/[ç]/g, 'c');
    };

    const objetoNorm = normalizar(objeto);
    const sugestoes: string[] = [];

    // 🧪 ÁLCOOL GEL
    if (objetoNorm.includes('alcool') || objetoNorm.includes('gel')) {
      sugestoes.push(`ÁLCOOL GEL 70% ${quantidade}${unidade}`);
      sugestoes.push(`ÁLCOOL GEL ANTISSÉPTICO 70% ${quantidade}${unidade}`);
      sugestoes.push(`GEL ANTISSÉPTICO ÁLCOOL 70% ${quantidade}${unidade}`);
    }

    // 📄 PAPEL
    else if (objetoNorm.includes('papel')) {
      sugestoes.push(`PAPEL A4 75G ${quantidade} ${unidade}`);
      sugestoes.push(`PAPEL SULFITE A4 ${quantidade} ${unidade}`);
      sugestoes.push(`PAPEL BRANCO A4 75G/M² ${quantidade} ${unidade}`);
    }

    // 💊 MEDICAMENTOS
    else if (objetoNorm.includes('dipirona')) {
      sugestoes.push(`DIPIRONA SÓDICA 500MG ${quantidade} ${unidade}`);
      sugestoes.push(`DIPIRONA 500MG COMPRIMIDO ${quantidade} ${unidade}`);
    }

    // 🧽 PRODUTOS DE LIMPEZA
    else if (objetoNorm.includes('detergente')) {
      sugestoes.push(`DETERGENTE NEUTRO ${quantidade}${unidade}`);
      sugestoes.push(`DETERGENTE LÍQUIDO ${quantidade}${unidade}`);
    }

    // 🎯 GENÉRICO - Melhora qualquer descrição
    else {
      const objetoCapitalizado = objeto.toUpperCase();
      sugestoes.push(`${objetoCapitalizado} ${quantidade}${unidade}`);
      sugestoes.push(`${objetoCapitalizado} ${quantidade} ${unidade}`);
    }

    return sugestoes.slice(0, 3); // Máximo 3 sugestões
  };

  // 📝 ATUALIZAR DADOS DO ITEM
  const atualizarDadosItem = (campo: string, valor: string) => {
    setDadosItem(prev => {
      const novoDado = { ...prev, [campo]: valor };

      // 🧠 INTELIGÊNCIA: Detectar unidades quando objeto muda
      if (campo === 'objeto' && valor.length > 3) {
        const sugestoes = detectarUnidadePorObjeto(valor);
        setSugestoesUnidade(sugestoes);
        setMostrarSugestoes(true);

        // Auto-preencher primeira sugestão se unidade estiver vazia
        if (!novoDado.unidade && sugestoes.length > 0) {
          novoDado.unidade = sugestoes[0];
        }
      }

      return novoDado;
    });
  };

  // 🔍 EXECUTAR PESQUISA MANUAL COMPLETA
  const executarPesquisaManual = async () => {
    if (!dadosItem.objeto.trim() || !dadosItem.quantidade || !dadosItem.unidade) {
      alert('⚠️ Preencha todos os campos obrigatórios!');
      return;
    }

    setShowInsercaoManual(false);
    setLoading(true);

    try {
      console.log('🔍 Iniciando pesquisa manual REAL no PNCP:', dadosItem);

      // 🚀 PESQUISA REAL NO PNCP COM DADOS ESTRUTURADOS
      const termoPesquisa = `${dadosItem.objeto} ${dadosItem.volume} ${dadosItem.concentracao} ${dadosItem.tipo}`.trim();

      const { PNCPApi } = await import('@/lib/pncpApi');

      const params = {
        termo: termoPesquisa,
        unidade: dadosItem.unidade,
        especificacao: dadosItem.especificacao,
        orgaoTipo: 'municipal' as const,
        dataInicio: '2024-01-01',
        dataFim: new Date().toISOString().split('T')[0]
      };

      console.log('📋 Parâmetros da pesquisa manual:', params);

      const resultadoPNCP = await PNCPApi.buscarItens(params);

      console.log('✅ Resultado pesquisa manual:', {
        total: resultadoPNCP.total,
        itens: resultadoPNCP.itens.length,
        media: resultadoPNCP.media
      });

      // 🔄 TRANSFORMAR DADOS PNCP PARA FORMATO DO SISTEMA
      const resultadosTransformados = resultadoPNCP.itens.map((item, index) => ({
        id: index + 1,
        item: dadosItem.objeto,
        categoria: 'Material Solicitado',
        precoMedio: item.preco || 0,
        menorPreco: item.preco * 0.9 || 0,
        maiorPreco: item.preco * 1.1 || 0,
        orgao: item.orgao || 'Órgão Público',
        dataLicitacao: item.dataContrato || new Date().toLocaleDateString('pt-BR'),
        modalidade: 'Pregão Eletrônico',
        status: 'Homologado',
        uf: 'SP',
        cidade: 'São Paulo',
        numeroLicitacao: item.numeroContrato || `${index + 1}/2025`,
        fornecedor: item.fornecedor || 'Fornecedor LTDA',
        unidade: dadosItem.unidade,
        quantidade: parseInt(dadosItem.quantidade),
        confiabilidade: item.relevancia || 85,
        especificacao: dadosItem.especificacao
      }));

      setResults(resultadosTransformados);
      setStats({
        totalResultados: resultadosTransformados.length,
        precoMedio: resultadoPNCP.media || 0,
        economiaEstimada: resultadosTransformados.length * 100
      });

      // Adicionar ao histórico com dados completos
      const novaConsulta = {
        id: Date.now().toString(),
        termo: `${dadosItem.objeto} (${dadosItem.quantidade} ${dadosItem.unidade})`,
        numeroProcesso: dadosItem.numeroProcesso,
        dataConsulta: new Date().toLocaleDateString('pt-BR'),
        resultados: resultadosTransformados.length,
        usuario: 'Marcos Isidoro',
        fonte: 'MANUAL' as const,
        status: resultadosTransformados.length > 0 ? 'Concluída' : 'Sem resultados'
      };
      setHistoricoPesquisas(prev => [novaConsulta, ...prev]);

      // Marcar pesquisa como concluída
      setPesquisaConcluida(true);
      setLoading(false);

      // 📢 FEEDBACK PARA O USUÁRIO
      if (resultadosTransformados.length === 0) {
        console.warn('⚠️ Nenhum resultado encontrado para:', termoPesquisa);
        // Não mostrar alert - apenas deixar vazio
      } else {
        console.log(`✅ ${resultadosTransformados.length} resultados encontrados!`);
      }

      // 🎯 SCROLL AUTOMÁTICO PARA RESULTADOS
      setTimeout(() => {
        const resultadosElement = document.getElementById('resultados-pesquisa');
        if (resultadosElement) {
          resultadosElement.scrollIntoView({ behavior: 'smooth' });
        }
      }, 500);

    } catch (error) {
      console.error('❌ Erro na pesquisa manual:', error);

      // 🔄 FALLBACK: Manter resultado vazio em caso de erro
      setResults([]);
      setStats({
        totalResultados: 0,
        precoMedio: 0,
        economiaEstimada: 0
      });

      // Adicionar ao histórico como erro
      const consultaErro = {
        id: Date.now().toString(),
        termo: `${dadosItem.objeto} (${dadosItem.quantidade} ${dadosItem.unidade})`,
        numeroProcesso: dadosItem.numeroProcesso,
        dataConsulta: new Date().toLocaleDateString('pt-BR'),
        resultados: 0,
        usuario: 'Marcos Isidoro',
        fonte: 'MANUAL' as const,
        status: 'Erro na consulta'
      };
      setHistoricoPesquisas(prev => [consultaErro, ...prev]);

      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Pesquisa de Preços</h1>
          <p className="text-muted-foreground mt-2">
            Consulte preços praticados em licitações públicas via PNCP
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <ExternalLink className="mr-1 h-3 w-3" />
            PNCP Integrado
          </Badge>
          <Button
            variant="outline"
            onClick={() => {
              setShowHistorico(!showHistorico);
              // Rolar para seção de histórico
              setTimeout(() => {
                const historicoElement = document.getElementById('secao-historico');
                if (historicoElement) {
                  historicoElement.scrollIntoView({ behavior: 'smooth' });
                }
              }, 100);
            }}
          >
            <History className="mr-2 h-4 w-4" />
            Histórico ({historicoPesquisas.length})
          </Button>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Exportar Relatório
          </Button>
        </div>
      </div>

      {/* Formulário de Pesquisa */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Search className="mr-2 h-5 w-5" />
            Nova Pesquisa de Preços
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Tabs - 3 BOTÕES PADRONIZADOS */}
          <div className="grid grid-cols-3 gap-2 bg-muted p-1 rounded-lg">
            <button
              onClick={() => setActiveTab('manual')}
              className={`px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'manual'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <Search className="inline-block w-4 h-4 mr-2" />
              Busca Manual
            </button>
            <button
              onClick={() => setActiveTab('tr')}
              className={`px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'tr'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <FileText className="inline-block w-4 h-4 mr-2" />
              Buscar no TR
            </button>
            <button
              onClick={() => setActiveTab('thema')}
              className={`px-4 py-3 text-sm font-medium rounded-md transition-colors ${
                activeTab === 'thema'
                  ? 'bg-background text-foreground shadow-sm'
                  : 'text-muted-foreground hover:text-foreground'
              }`}
            >
              <Building className="inline-block w-4 h-4 mr-2" />
              Buscar Thema
            </button>
          </div>

          {/* Conteúdo das Tabs */}
          {activeTab === 'manual' ? (
            <div className="space-y-4">
              {/* INSERÇÃO MANUAL DE ITENS */}
              <div className="text-center p-6 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Inserir Itens Manualmente</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  • Especificar itens e quantidades<br/>
                  • Para pesquisa no PNCP
                </p>
                <Button
                  className="w-full max-w-md"
                  onClick={() => setShowInsercaoManual(true)}
                >
                  <Search className="mr-2 h-4 w-4" />
                  Inserir Itens
                </Button>
              </div>
            </div>
          ) : activeTab === 'tr' ? (
            <div className="space-y-4">
              <div className="text-center p-6 border-2 border-dashed border-muted-foreground/25 rounded-lg">
                <FileText className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
                <h3 className="text-lg font-medium mb-2">Extrair do TR</h3>
                <p className="text-sm text-muted-foreground mb-4">
                  • Extrair lotes do TR aprovado<br/>
                  • Itens com especificações<br/>
                  • Para pesquisa no PNCP
                </p>
                <div className="space-y-3">
                  <Input
                    placeholder="Ex: 907/2025, PC-2025-001, REQ-456"
                    className="max-w-md mx-auto"
                  />
                  <Button className="w-full max-w-md">
                    <Download className="mr-2 h-4 w-4" />
                    Extrair do TR
                  </Button>
                </div>
              </div>
            </div>
          ) : activeTab === 'thema' ? (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-2">
                    Número do Processo ou Pedido de Compra
                  </label>
                  <Input
                    placeholder="Ex: 9078/2025, PC-2025-001, REQ-456"
                    value={numeroProcesso}
                    onChange={(e) => setNumeroProcesso(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleBuscarThema}
                    disabled={loadingThema || !numeroProcesso.trim()}
                    className="w-full"
                  >
                    {loadingThema ? (
                      <>
                        <Clock className="mr-2 h-4 w-4 animate-spin" />
                        Buscando...
                      </>
                    ) : (
                      <>
                        <Building className="mr-2 h-4 w-4" />
                        Buscar no Thema
                      </>
                    )}
                  </Button>
                </div>
              </div>

              <div className="bg-blue-50 dark:bg-blue-900/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-3">
                  <div className="bg-blue-100 dark:bg-blue-900/40 p-2 rounded-lg">
                    <ExternalLink className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                  </div>
                  <div>
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-1">
                      Integração com Sistema Thema
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      Digite o número do processo ou pedido de compra para importar automaticamente
                      todos os itens e realizar pesquisa inteligente no PNCP com análise por IA.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-2">
                    Item ou Serviço
                  </label>
                  <Input
                    placeholder="Digite o item ou serviço que deseja pesquisar..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="w-full"
                  />
                </div>
                <div className="flex items-end">
                  <Button
                    onClick={handleSearch}
                    disabled={loading || !searchTerm.trim()}
                    className="w-full"
                  >
                    {loading ? (
                      <>
                        <Clock className="mr-2 h-4 w-4 animate-spin" />
                        Pesquisando...
                      </>
                    ) : (
                      <>
                        <Search className="mr-2 h-4 w-4" />
                        Pesquisar no PNCP
                      </>
                    )}
                  </Button>
                </div>
              </div>
            </div>
          )}
          
          <div className="flex flex-wrap gap-2">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-3 w-3" />
              Filtros Avançados
            </Button>
            <Button variant="outline" size="sm">
              <Calendar className="mr-2 h-3 w-3" />
              Período
            </Button>
            <Button variant="outline" size="sm">
              <MapPin className="mr-2 h-3 w-3" />
              Localização
            </Button>
            <Button variant="outline" size="sm">
              <Building className="mr-2 h-3 w-3" />
              Órgão
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Histórico de Pesquisas */}
      {showHistorico && (
        <Card id="secao-historico">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <History className="mr-2 h-5 w-5" />
                Histórico de Pesquisas
              </span>
              <Badge variant="outline">{historicoPesquisas.length} pesquisas</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {historicoPesquisas.map((pesquisa) => (
                <div key={pesquisa.id} className="flex items-center justify-between p-3 border border-border rounded-lg hover:bg-muted/50">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3">
                      <div className="flex-1">
                        <h4 className="font-medium">{pesquisa.termo}</h4>
                        <p className="text-sm text-muted-foreground">
                          {pesquisa.dataConsulta} • {pesquisa.resultados} resultados • {pesquisa.usuario}
                        </p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <Badge variant="outline" className="text-xs">
                          {pesquisa.fonte}
                        </Badge>
                        <Badge variant="secondary" className="text-xs">
                          <CheckCircle className="mr-1 h-3 w-3" />
                          {pesquisa.status}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Button variant="ghost" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Resultados da Pesquisa PNCP */}
      {results.length > 0 && (
        <Card id="resultados-pesquisa">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <Search className="mr-2 h-5 w-5" />
                Resultados da Pesquisa PNCP
              </span>
              <Badge variant="outline">{results.length} resultados</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {/* Estatísticas */}
              {stats.totalResultados && (
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 bg-muted/50 rounded-lg">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-primary">{stats.totalResultados}</div>
                    <div className="text-sm text-muted-foreground">Resultados</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-green-600">R$ {stats.precoMedio?.toFixed(2)}</div>
                    <div className="text-sm text-muted-foreground">Preço Médio</div>
                  </div>
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">R$ {stats.economiaEstimada?.toFixed(2)}</div>
                    <div className="text-sm text-muted-foreground">Economia Estimada</div>
                  </div>
                </div>
              )}

              {/* 💡 BANNER DE SUGESTÕES INTELIGENTES */}
              {stats.tipoResultado === 'sugestoes' && (
                <div className="bg-orange-50 dark:bg-orange-950/20 border border-orange-200 dark:border-orange-800 rounded-lg p-4">
                  <div className="flex items-start space-x-3">
                    <div className="flex-shrink-0 mt-1">
                      <div className="w-6 h-6 bg-orange-100 dark:bg-orange-900 rounded-full flex items-center justify-center">
                        <span className="text-orange-600 dark:text-orange-400 text-sm">💡</span>
                      </div>
                    </div>
                    <div className="flex-1">
                      <h4 className="font-semibold text-orange-900 dark:text-orange-100 mb-2">
                        🔍 Sugestões Inteligentes Encontradas
                      </h4>
                      <p className="text-sm text-orange-700 dark:text-orange-300 mb-3">
                        Não encontramos resultados exatos para sua busca, mas encontramos <strong>{results.length} itens relacionados</strong> que podem ser úteis:
                      </p>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
                        <div className="flex items-center space-x-2">
                          <span className="text-orange-600">📦</span>
                          <span>Diferentes volumes/embalagens</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-orange-600">🧪</span>
                          <span>Estados físicos similares</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-orange-600">📏</span>
                          <span>Unidades de medida relacionadas</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-orange-600">🎯</span>
                          <span>Concentrações/especificações próximas</span>
                        </div>
                      </div>
                      <div className="mt-3 p-2 bg-background rounded border border-orange-200 dark:border-orange-700">
                        <p className="text-xs text-muted-foreground">
                          <strong>💡 Dica:</strong> Analise os itens abaixo para verificar se algum atende suas necessidades ou pode ser adaptado.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Lista de Resultados */}
              <div className="space-y-3">
                {results.map((resultado) => (
                  <div key={resultado.id} className="border border-border rounded-lg p-4 hover:bg-muted/50">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <h4 className="font-medium mb-2">{resultado.item}</h4>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                          <div>
                            <span className="text-muted-foreground">Órgão:</span>
                            <p className="font-medium">{resultado.orgao}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Modalidade:</span>
                            <p className="font-medium">{resultado.modalidade}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Data:</span>
                            <p className="font-medium">{resultado.dataLicitacao}</p>
                          </div>
                          <div>
                            <span className="text-muted-foreground">Status:</span>
                            <Badge variant={resultado.status === 'Homologado' ? 'default' : 'secondary'}>
                              {resultado.status}
                            </Badge>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4 mt-3">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">Preço:</span>
                            <Badge variant="outline" className="text-green-600">
                              R$ {resultado.menorPreco?.toFixed(2)} - R$ {resultado.maiorPreco?.toFixed(2)}
                            </Badge>
                          </div>
                          <div className="flex items-center space-x-2">
                            <span className="text-sm text-muted-foreground">Médio:</span>
                            <Badge className="bg-blue-100 text-blue-800">
                              R$ {resultado.precoMedio?.toFixed(2)}
                            </Badge>
                          </div>
                          {resultado.confiabilidade && (
                            <div className="flex items-center space-x-2">
                              <span className="text-sm text-muted-foreground">
                                {resultado.confiabilidade < 90 ? 'Relevância:' : 'Confiabilidade:'}
                              </span>
                              <Badge
                                variant={resultado.confiabilidade < 90 ? "outline" : "secondary"}
                                className={resultado.confiabilidade < 90 ? "border-orange-300 text-orange-700" : ""}
                              >
                                {resultado.confiabilidade < 90 ? '💡' : ''} {resultado.confiabilidade}%
                                {resultado.confiabilidade < 90 ? ' sugestão' : ''}
                              </Badge>
                            </div>
                          )}
                        </div>
                      </div>
                      <Button variant="ghost" size="sm">
                        <Eye className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                ))}
              </div>

              {/* Ações */}
              <div className="flex justify-center space-x-3 pt-4">
                <Button variant="outline">
                  <Download className="mr-2 h-4 w-4" />
                  Gerar Mapa de Preços
                </Button>
                <Button variant="outline">
                  <FileText className="mr-2 h-4 w-4" />
                  Gerar Despacho
                </Button>
              </div>
            </div>

            {/* 🎯 DECISÃO: AMPLIAR PESQUISA REGIONAL */}
            {pesquisaConcluida && (
              <div className="mt-6 p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                <div className="flex items-start space-x-3">
                  <div className="flex-shrink-0 mt-1">
                    <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 dark:text-blue-400 text-sm font-bold">?</span>
                    </div>
                  </div>
                  <div className="flex-1">
                    <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                      ⚖️ Pesquisa PNCP Concluída - Próximo Passo
                    </h4>
                    <p className="text-sm text-blue-700 dark:text-blue-300 mb-4">
                      Conforme <strong>Lei 14.133/21</strong>, a pesquisa no PNCP foi realizada com sucesso.
                      Agora você pode optar por ampliar com pesquisa regional para fundamentação adicional.
                    </p>

                    <div className="flex items-center space-x-3 mb-4">
                      <input
                        type="checkbox"
                        id="ampliar-pesquisa"
                        checked={ampliarPesquisa}
                        onChange={(e) => setAmpliarPesquisa(e.target.checked)}
                        className="w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500"
                      />
                      <label htmlFor="ampliar-pesquisa" className="text-sm font-medium text-blue-900 dark:text-blue-100">
                        📧 Ampliar com pesquisa regional (fornecedores locais)
                      </label>
                    </div>

                    <div className="flex space-x-3">
                      <Button
                        onClick={() => {
                          if (ampliarPesquisa) {
                            setShowCotacoesBanner(true);
                          } else {
                            gerarDespacho();
                          }
                        }}
                        className="flex-1"
                      >
                        {ampliarPesquisa ? '📧 Continuar Pesquisa Combinada' : '📋 Finalizar Pesquisa Simples'}
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* 📧 BANNER PARA COLAR COTAÇÕES - PESQUISA COMBINADA */}
      {showCotacoesBanner && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="mr-2">📧</span>
              Pesquisa Combinada - Inserir Cotações Regionais
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-blue-50 dark:bg-blue-950/20 p-4 rounded-lg border border-blue-200 dark:border-blue-800">
                <h4 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  📋 Cole aqui todas as cotações recebidas
                </h4>
                <p className="text-sm text-blue-700 dark:text-blue-300 mb-3">
                  • Emails de fornecedores com cotações<br/>
                  • Planilhas de preços<br/>
                  • Propostas comerciais<br/>
                  • A IA irá extrair automaticamente os dados por fornecedor
                </p>
              </div>

              <div className="space-y-3">
                <label className="block text-sm font-medium">
                  Cole todo o conteúdo das cotações (emails, planilhas, etc.)
                </label>
                <textarea
                  value={cotacoesTexto}
                  onChange={(e) => setCotacoesTexto(e.target.value)}
                  placeholder="Cole aqui todos os emails, planilhas e cotações recebidas dos fornecedores..."
                  className="w-full h-64 p-3 border border-border rounded-lg resize-none"
                />
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={() => {
                    // Simular extração por IA
                    setDadosExtraidos([
                      { fornecedor: 'Empresa A', item: 'Álcool 70% 500ml', preco: 15.50, unidade: 'frasco' },
                      { fornecedor: 'Empresa B', item: 'Álcool etílico 70% 500ml', preco: 14.80, unidade: 'unidade' },
                      { fornecedor: 'Empresa C', item: 'Álcool gel 70% 500ml', preco: 16.20, unidade: 'frasco' }
                    ]);
                    setShowAprovacaoExtracao(true);
                    setShowCotacoesBanner(false);
                  }}
                  disabled={!cotacoesTexto.trim()}
                  className="flex-1"
                >
                  <span className="mr-2">🤖</span>
                  Inserir Cotações no Mapa
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowCotacoesBanner(false)}
                >
                  Cancelar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* ✅ TELA DE APROVAÇÃO DAS EXTRAÇÕES */}
      {showAprovacaoExtracao && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <span className="mr-2">✅</span>
              Aprovar Dados Extraídos pela IA
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="bg-green-50 dark:bg-green-950/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <h4 className="font-semibold text-green-900 dark:text-green-100 mb-2">
                  🤖 IA extraiu os seguintes dados das cotações
                </h4>
                <p className="text-sm text-green-700 dark:text-green-300">
                  Verifique se os dados estão corretos antes de inserir no mapa de preços
                </p>
              </div>

              <div className="space-y-3">
                {dadosExtraidos.map((item, index) => (
                  <div key={index} className="border border-border rounded-lg p-3">
                    <div className="grid grid-cols-4 gap-3 text-sm">
                      <div>
                        <span className="font-medium">Fornecedor:</span>
                        <p>{item.fornecedor}</p>
                      </div>
                      <div>
                        <span className="font-medium">Item:</span>
                        <p>{item.item}</p>
                      </div>
                      <div>
                        <span className="font-medium">Preço:</span>
                        <p>R$ {item.preco.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="font-medium">Unidade:</span>
                        <p>{item.unidade}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              <div className="flex space-x-3">
                <Button
                  onClick={() => {
                    alert('✅ Mapa gerado! Excluindo preços ±50%, gerando despacho de pesquisa combinada...');
                    setShowAprovacaoExtracao(false);
                  }}
                  className="flex-1"
                >
                  ✅ Aprovar e Gerar Mapa Combinado
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowAprovacaoExtracao(false)}
                >
                  ❌ Rejeitar
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Lista de Itens do Thema */}
      {themaItems.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Itens Encontrados no Thema</span>
              <Badge variant="outline">{themaItems.length} itens</Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <p className="text-sm text-muted-foreground">
                  Selecione os itens para pesquisa automática no PNCP com análise por IA
                </p>
                <div className="flex space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedItems(themaItems.map(item => item.id))}
                  >
                    Selecionar Todos
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setSelectedItems([])}
                  >
                    Limpar Seleção
                  </Button>
                </div>
              </div>

              <div className="grid gap-4">
                {themaItems.map((item) => (
                  <div key={item.id} className="border border-border rounded-lg p-4">
                    <div className="flex items-start space-x-3">
                      <input
                        type="checkbox"
                        id={`item-${item.id}`}
                        checked={selectedItems.includes(item.id)}
                        onChange={(e) => {
                          if (e.target.checked) {
                            setSelectedItems([...selectedItems, item.id]);
                          } else {
                            setSelectedItems(selectedItems.filter(id => id !== item.id));
                          }
                        }}
                        className="mt-1 h-4 w-4 text-primary focus:ring-primary border-gray-300 rounded"
                      />
                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-foreground">
                            {item.codigo} - {item.descricao}
                          </h4>
                          <Badge variant="secondary">{item.categoria}</Badge>
                        </div>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-2 text-sm text-muted-foreground">
                          <div>
                            <span className="font-medium">Quantidade:</span> {item.quantidade} {item.unidade}
                          </div>
                          <div>
                            <span className="font-medium">Valor Unit.:</span> R$ {item.valorUnitario.toFixed(2)}
                          </div>
                          <div>
                            <span className="font-medium">Valor Total:</span> R$ {item.valorTotal.toFixed(2)}
                          </div>
                          <div>
                            <span className="font-medium">Unidade:</span> {item.unidade}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>

              {selectedItems.length > 0 && (
                <div className="flex justify-center pt-4">
                  <Button
                    onClick={handlePesquisarItensSelecionados}
                    disabled={loading}
                    className="bg-primary hover:bg-primary/90"
                  >
                    {loading ? (
                      <>
                        <Clock className="mr-2 h-4 w-4 animate-spin" />
                        Analisando com IA...
                      </>
                    ) : (
                      <>
                        <TrendingUp className="mr-2 h-4 w-4" />
                        Pesquisar {selectedItems.length} itens no PNCP (IA)
                      </>
                    )}
                  </Button>
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Mapa de Preços */}
      {mapaPreco && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Mapa de Preços - Processo {mapaPreco.numeroProcesso}</span>
              <div className="flex space-x-2">
                <Button variant="outline" size="sm" onClick={gerarDespacho}>
                  <FileText className="mr-2 h-3 w-3" />
                  Gerar Despacho
                </Button>
                <Button variant="outline" size="sm">
                  <Download className="mr-2 h-3 w-3" />
                  Exportar PDF
                </Button>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* Alertas de Unidades */}
              {mapaPreco.alertasUnidades && mapaPreco.alertasUnidades.length > 0 && (
                <div className="bg-yellow-50 dark:bg-yellow-900/20 p-4 rounded-lg border border-yellow-200 dark:border-yellow-800">
                  <h4 className="font-medium text-yellow-900 dark:text-yellow-100 mb-3 flex items-center">
                    <span className="mr-2">⚠️</span>
                    Alertas de Unidades Diferentes
                  </h4>
                  <div className="space-y-2">
                    {mapaPreco.alertasUnidades.map((alerta: any, index: number) => (
                      <div key={index} className="bg-background p-3 rounded border border-yellow-300 dark:border-yellow-700">
                        <div className="flex items-center justify-between">
                          <div>
                            <p className="font-medium text-sm">{alerta.item}</p>
                            <p className="text-xs text-muted-foreground">{alerta.fornecedor}</p>
                          </div>
                          <div className="text-right">
                            <p className="text-sm font-medium">{alerta.conversao}</p>
                            <p className="text-xs text-yellow-700 dark:text-yellow-300">{alerta.observacao}</p>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Resumo */}
              <div className="bg-green-50 dark:bg-green-900/20 p-4 rounded-lg border border-green-200 dark:border-green-800">
                <h4 className="font-medium text-green-900 dark:text-green-100 mb-3">Resumo da Análise</h4>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                  <div>
                    <span className="text-green-700 dark:text-green-300">Valor Original:</span>
                    <p className="font-bold text-green-900 dark:text-green-100">
                      R$ {mapaPreco.resumo.valorOriginal.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <span className="text-green-700 dark:text-green-300">Valor Recomendado:</span>
                    <p className="font-bold text-green-900 dark:text-green-100">
                      R$ {mapaPreco.resumo.valorRecomendado.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <span className="text-green-700 dark:text-green-300">Economia Total:</span>
                    <p className="font-bold text-green-900 dark:text-green-100">
                      R$ {mapaPreco.resumo.economiaTotal.toFixed(2)}
                    </p>
                  </div>
                  <div>
                    <span className="text-green-700 dark:text-green-300">% Economia:</span>
                    <p className="font-bold text-green-900 dark:text-green-100">
                      {mapaPreco.resumo.percentualEconomia}%
                    </p>
                  </div>
                </div>
              </div>

              {/* Itens Analisados */}
              <div className="space-y-4">
                <h4 className="font-medium">Itens Analisados</h4>
                {mapaPreco.itens.map((item: any, index: number) => (
                  <div key={index} className="border border-border rounded-lg p-4">
                    <div className="flex items-center justify-between mb-3">
                      <h5 className="font-medium">{item.itemOriginal.descricao}</h5>
                      <Badge className="bg-blue-100 text-blue-800">
                        Confiabilidade: {item.analiseIA.confiabilidadeGeral}%
                      </Badge>
                    </div>
                    <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                      <div>
                        <span className="text-muted-foreground">Preço Original:</span>
                        <p className="font-medium">R$ {item.itemOriginal.valorUnitario.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Média PNCP:</span>
                        <p className="font-medium">R$ {item.analiseIA.mediaCalculada.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Mediana PNCP:</span>
                        <p className="font-medium">R$ {item.analiseIA.medianaCalculada.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Preço Recomendado:</span>
                        <p className="font-medium text-green-600">R$ {item.analiseIA.precoRecomendado.toFixed(2)}</p>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Economia:</span>
                        <p className="font-medium text-green-600">R$ {item.analiseIA.economiaEstimada.toFixed(2)}</p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Modal de Inserção Manual COMPLETO */}
      {showInsercaoManual && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50">
          <div className="bg-background border border-border rounded-lg p-6 w-full max-w-3xl max-h-[90vh] overflow-y-auto">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-xl font-bold">📝 Busca Manual - Inserir Item</h2>
                <p className="text-sm text-muted-foreground mt-1">
                  Preencha os dados do item para pesquisa no PNCP
                </p>
              </div>
              <Button variant="ghost" onClick={() => setShowInsercaoManual(false)}>✕</Button>
            </div>

            <div className="space-y-6">
              {/* Número do Processo */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Número do Processo: <span className="text-red-500">*</span>
                </label>
                <Input
                  placeholder="Ex: 2025/001, PC-2025-001, REQ-456"
                  value={dadosItem.numeroProcesso}
                  onChange={(e) => atualizarDadosItem('numeroProcesso', e.target.value)}
                />
              </div>

              {/* Objeto */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Objeto/Descrição do Item: <span className="text-red-500">*</span>
                </label>
                <Input
                  placeholder="Ex: Álcool gel antisséptico 70%, Papel A4 sulfite branco"
                  value={dadosItem.objeto}
                  onChange={(e) => atualizarDadosItem('objeto', e.target.value)}
                />
                {dadosItem.objeto.length > 3 && (
                  <p className="text-xs text-green-600 mt-1">
                    ✅ Unidades sugeridas automaticamente baseadas no item
                  </p>
                )}
              </div>

              {/* Quantidade e Unidade */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Quantidade: <span className="text-red-500">*</span>
                  </label>
                  <Input
                    placeholder="Ex: 100"
                    type="number"
                    min="1"
                    value={dadosItem.quantidade}
                    onChange={(e) => atualizarDadosItem('quantidade', e.target.value)}
                  />
                </div>
                <div className="relative">
                  <label className="block text-sm font-medium mb-2">
                    Unidade: <span className="text-red-500">*</span>
                  </label>
                  <Input
                    placeholder="Ex: FRASCO, UN, CX, L"
                    value={dadosItem.unidade}
                    onChange={(e) => atualizarDadosItem('unidade', e.target.value)}
                    onFocus={() => setMostrarSugestoes(true)}
                  />

                  {/* 🧠 SUGESTÕES INTELIGENTES DE UNIDADE */}
                  {mostrarSugestoes && sugestoesUnidade.length > 0 && (
                    <div className="absolute top-full left-0 right-0 bg-background border border-border rounded-md shadow-lg z-10 mt-1">
                      <div className="p-2 border-b border-border">
                        <p className="text-xs text-muted-foreground">💡 Sugestões baseadas no item:</p>
                      </div>
                      {sugestoesUnidade.map((unidade, index) => (
                        <button
                          key={index}
                          className="w-full text-left px-3 py-2 hover:bg-muted text-sm"
                          onClick={() => {
                            atualizarDadosItem('unidade', unidade);
                            setMostrarSugestoes(false);
                          }}
                        >
                          {unidade}
                        </button>
                      ))}
                      <div className="p-2 border-t border-border">
                        <button
                          className="text-xs text-muted-foreground hover:text-foreground"
                          onClick={() => setMostrarSugestoes(false)}
                        >
                          ✕ Fechar sugestões
                        </button>
                      </div>
                    </div>
                  )}
                </div>
              </div>

              {/* Especificação */}
              <div>
                <label className="block text-sm font-medium mb-2">
                  Especificação Técnica:
                </label>
                <textarea
                  className="w-full p-3 border border-border rounded-md bg-background text-foreground resize-none"
                  rows={4}
                  placeholder="Descreva as especificações técnicas detalhadas do item (concentração, marca, modelo, características especiais, etc.)"
                  value={dadosItem.especificacao}
                  onChange={(e) => atualizarDadosItem('especificacao', e.target.value)}
                />
              </div>

              {/* Preview dos dados */}
              {dadosItem.objeto && dadosItem.quantidade && dadosItem.unidade && (
                <div className="bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    📋 Preview da Pesquisa:
                  </h4>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    <strong>Processo:</strong> {dadosItem.numeroProcesso || 'Não informado'}<br/>
                    <strong>Item:</strong> {dadosItem.objeto}<br/>
                    <strong>Quantidade:</strong> {dadosItem.quantidade} {dadosItem.unidade}<br/>
                    <strong>Termo PNCP:</strong> "{dadosItem.objeto} {dadosItem.unidade}"
                  </p>
                </div>
              )}

              {/* Botões */}
              <div className="flex justify-end space-x-3 pt-4 border-t border-border">
                <Button
                  variant="outline"
                  onClick={() => {
                    setShowInsercaoManual(false);
                    setDadosItem({
                      numeroProcesso: '',
                      objeto: '',
                      quantidade: '',
                      unidade: '',
                      especificacao: ''
                    });
                  }}
                >
                  Cancelar
                </Button>
                <Button
                  onClick={executarPesquisaManual}
                  disabled={!dadosItem.objeto.trim() || !dadosItem.quantidade || !dadosItem.unidade}
                  className="min-w-[200px]"
                >
                  <Search className="mr-2 h-4 w-4" />
                  Pesquisar no PNCP
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Modal de Despacho */}
      {showDespacho && (
        <Card>
          <CardHeader>
            <CardTitle>Despacho para Secretaria Requisitante</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="bg-gray-50 dark:bg-gray-900 p-6 rounded-lg border">
              <div className="space-y-4 text-sm">
                <div className="text-center">
                  <h3 className="font-bold text-lg">PREFEITURA MUNICIPAL</h3>
                  <h4 className="font-bold">COORDENADORIA DE LICITAÇÕES, MATERIAIS E PATRIMÔNIO</h4>
                  <p className="mt-2">DESPACHO DE PESQUISA DE PREÇOS</p>
                </div>

                <div className="space-y-2">
                  <p><strong>Processo:</strong> {mapaPreco?.numeroProcesso}</p>
                  <p><strong>Data:</strong> {new Date().toLocaleDateString('pt-BR')}</p>
                  <p><strong>Assunto:</strong> Pesquisa de Preços - Análise Comparativa PNCP</p>
                </div>

                <div className="space-y-2">
                  <p className="font-medium">Senhor(a) Secretário(a),</p>
                  <p className="text-justify">
                    Em atenção ao processo em epígrafe, informamos que foi realizada pesquisa de preços
                    no Portal Nacional de Contratações Públicas (PNCP) com análise automatizada por
                    Inteligência Artificial, conforme estabelecido no Decreto Municipal nº 9337/2024.
                  </p>
                  <p className="text-justify">
                    A análise comparativa demonstrou uma economia potencial de <strong>
                    R$ {mapaPreco?.resumo.economiaTotal.toFixed(2)} ({mapaPreco?.resumo.percentualEconomia}%)
                    </strong> em relação aos valores originalmente estimados.
                  </p>
                  <p className="text-justify">
                    Recomendamos a adoção dos valores constantes no mapa de preços anexo,
                    que foram calculados com base na mediana dos preços praticados por órgãos
                    públicos similares, garantindo economicidade e conformidade com o art. 23
                    da Lei 14.133/2021.
                  </p>
                  <p className="text-justify">
                    Encaminhamos para conhecimento e providências cabíveis.
                  </p>
                </div>

                <div className="mt-6 text-center">
                  <p>Atenciosamente,</p>
                  <p className="mt-4 font-medium">CLMP - Coordenadoria de Licitações, Materiais e Patrimônio</p>
                </div>
              </div>
            </div>

            <div className="flex justify-end space-x-2 mt-4">
              <Button variant="outline" onClick={() => setShowDespacho(false)}>
                Fechar
              </Button>
              <Button>
                <Download className="mr-2 h-4 w-4" />
                Baixar Despacho
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Estatísticas Rápidas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Pesquisas Hoje</p>
                <p className="text-2xl font-bold">24</p>
              </div>
              <Search className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Economia Estimada</p>
                <p className="text-2xl font-bold text-green-600">R$ 45.2K</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Itens Pesquisados</p>
                <p className="text-2xl font-bold">156</p>
              </div>
              <DollarSign className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Órgãos Consultados</p>
                <p className="text-2xl font-bold">89</p>
              </div>
              <Building className="h-8 w-8 text-purple-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resultados */}
      <Card>
        <CardHeader>
          <CardTitle>Resultados da Pesquisa</CardTitle>
        </CardHeader>
        <CardContent>
          {results.length > 0 ? (
            <div className="space-y-4">
              {results.map((result, index) => {
                // 🔧 VERIFICAR TIPO DE RESULTADO: Thema (com IA) ou Manual/PNCP
                const isThemaResult = result.itemOriginal && result.analiseIA && result.resultadosPNCP;

                if (isThemaResult) {
                  // 🧠 RESULTADO DO THEMA COM ANÁLISE IA
                  return (
                    <div key={index} className="border border-border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-foreground">{result.itemOriginal.descricao}</h3>
                          <Badge className="bg-blue-100 text-blue-800">
                            IA: {result.analiseIA.confiabilidadeGeral}% confiável
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Resultados PNCP Encontrados:</h4>
                            {result.resultadosPNCP.map((pncp: any, pncpIndex: number) => (
                              <div key={pncpIndex} className="bg-muted/50 p-3 rounded border">
                                <div className="flex items-center justify-between mb-2">
                                  <span className="text-sm font-medium">{pncp.orgao}</span>
                                  <Badge variant="outline" className="text-xs">
                                    {pncp.confiabilidade}% match
                                  </Badge>
                                </div>
                                <div className="grid grid-cols-3 gap-2 text-xs">
                                  <div>
                                    <span className="text-muted-foreground">Menor:</span>
                                    <p className="font-medium text-green-600">
                                      R$ {pncp.menorPreco.toFixed(2)}
                                    </p>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Médio:</span>
                                    <p className="font-medium">
                                      R$ {pncp.precoMedio.toFixed(2)}
                                    </p>
                                  </div>
                                  <div>
                                    <span className="text-muted-foreground">Maior:</span>
                                    <p className="font-medium text-red-600">
                                      R$ {pncp.maiorPreco.toFixed(2)}
                                    </p>
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>

                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Análise da IA:</h4>
                            <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded border border-green-200 dark:border-green-800">
                              <div className="grid grid-cols-2 gap-2 text-xs">
                                <div>
                                  <span className="text-green-700 dark:text-green-300">Preço Original:</span>
                                  <p className="font-medium">R$ {result.itemOriginal.valorUnitario.toFixed(2)}</p>
                                </div>
                                <div>
                                  <span className="text-green-700 dark:text-green-300">Média PNCP:</span>
                                  <p className="font-medium">R$ {result.analiseIA.mediaCalculada.toFixed(2)}</p>
                                </div>
                                <div>
                                  <span className="text-green-700 dark:text-green-300">Mediana PNCP:</span>
                                  <p className="font-medium">R$ {result.analiseIA.medianaCalculada.toFixed(2)}</p>
                                </div>
                                <div>
                                  <span className="text-green-700 dark:text-green-300">Recomendado:</span>
                                  <p className="font-bold text-green-800 dark:text-green-200">
                                    R$ {result.analiseIA.precoRecomendado.toFixed(2)}
                                  </p>
                                </div>
                              </div>
                              <div className="mt-2 pt-2 border-t border-green-200 dark:border-green-700">
                                <span className="text-green-700 dark:text-green-300 text-xs">Economia Estimada:</span>
                                <p className="font-bold text-green-800 dark:text-green-200">
                                  R$ {result.analiseIA.economiaEstimada.toFixed(2)}
                                  ({result.itemOriginal.quantidade} {result.itemOriginal.unidade})
                                </p>
                              </div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                } else {
                  // 📋 RESULTADO MANUAL/PNCP SIMPLES
                  return (
                    <div key={index} className="border border-border rounded-lg p-4 hover:bg-accent/50 transition-colors">
                      <div className="space-y-4">
                        <div className="flex items-center justify-between">
                          <h3 className="font-semibold text-foreground">{result.item}</h3>
                          <Badge className="bg-green-100 text-green-800">
                            {result.confiabilidade}% match
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Informações:</h4>
                            <div className="text-sm space-y-1">
                              <p><span className="text-muted-foreground">Órgão:</span> {result.orgao}</p>
                              <p><span className="text-muted-foreground">Modalidade:</span> {result.modalidade}</p>
                              <p><span className="text-muted-foreground">Data:</span> {result.dataLicitacao}</p>
                              <p><span className="text-muted-foreground">Status:</span> {result.status}</p>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Preços Encontrados:</h4>
                            <div className="grid grid-cols-3 gap-2 text-xs">
                              <div>
                                <span className="text-muted-foreground">Menor:</span>
                                <p className="font-medium text-green-600">
                                  R$ {result.menorPreco.toFixed(2)}
                                </p>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Médio:</span>
                                <p className="font-medium">
                                  R$ {result.precoMedio.toFixed(2)}
                                </p>
                              </div>
                              <div>
                                <span className="text-muted-foreground">Maior:</span>
                                <p className="font-medium text-red-600">
                                  R$ {result.maiorPreco.toFixed(2)}
                                </p>
                              </div>
                            </div>
                          </div>

                          <div className="space-y-2">
                            <h4 className="font-medium text-sm">Detalhes:</h4>
                            <div className="text-sm space-y-1">
                              <p><span className="text-muted-foreground">Fornecedor:</span> {result.fornecedor}</p>
                              <p><span className="text-muted-foreground">Unidade:</span> {result.unidade}</p>
                              <p><span className="text-muted-foreground">Quantidade:</span> {result.quantidade}</p>
                              <p><span className="text-muted-foreground">Local:</span> {result.cidade}/{result.uf}</p>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                }
              })}
            </div>
          ) : (
            <div className="text-center py-8">
              <Search className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium text-foreground mb-2">
                {activeTab === 'thema' ? 'Nenhum item selecionado' : 'Nenhuma pesquisa realizada'}
              </h3>
              <p className="text-muted-foreground">
                {activeTab === 'thema'
                  ? 'Busque um processo no Thema e selecione os itens para análise automática'
                  : 'Digite um item ou serviço acima para começar a pesquisar preços no PNCP'
                }
              </p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* 🤖 MODAL: IA PARSING DE TEXTO */}
      {showParsingIA && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background border border-border rounded-lg shadow-lg w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold flex items-center">
                  <span className="mr-2">🤖</span>
                  IA: Extrair Dados de Texto
                </h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowParsingIA(false)}
                >
                  ✕
                </Button>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium mb-2">
                    Cole o texto do TR, email ou planilha:
                  </label>
                  <textarea
                    value={textoParaParsing}
                    onChange={(e) => setTextoParaParsing(e.target.value)}
                    placeholder="Exemplo: ÁLCOOL GEL ANTISSÉPTICO 70% INPM 500ML - 100 FRASCOS"
                    className="w-full h-32 p-3 border border-border rounded-md resize-none bg-background text-foreground"
                  />
                </div>

                <div className="flex space-x-3">
                  <Button
                    onClick={() => {
                      const dadosExtraidos = parsearTextoComIA(textoParaParsing);
                      setDadosItem(prev => ({
                        ...prev,
                        ...dadosExtraidos
                      }));
                      setShowParsingIA(false);
                      setShowInsercaoManual(true);
                    }}
                    className="flex-1 bg-purple-600 hover:bg-purple-700"
                    disabled={!textoParaParsing.trim()}
                  >
                    <span className="mr-2">🤖</span>
                    Extrair com IA
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => setShowParsingIA(false)}
                  >
                    Cancelar
                  </Button>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* 📝 MODAL: INSERÇÃO MANUAL */}
      {showInsercaoManual && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
          <div className="bg-background border border-border rounded-lg shadow-lg w-full max-w-4xl max-h-[90vh] overflow-y-auto">
            <div className="p-6">
              <div className="flex items-center justify-between mb-6">
                <h2 className="text-xl font-bold">Inserir Item para Pesquisa</h2>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setShowInsercaoManual(false)}
                >
                  ✕
                </Button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* COLUNA 1: DADOS BÁSICOS */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Número do Processo: *
                    </label>
                    <input
                      type="text"
                      value={dadosItem.numeroProcesso}
                      onChange={(e) => atualizarDadosItem('numeroProcesso', e.target.value)}
                      placeholder="Ex: 9999/2025"
                      className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Objeto/Descrição do Item: *
                    </label>
                    <input
                      type="text"
                      value={dadosItem.objeto}
                      onChange={(e) => atualizarDadosItem('objeto', e.target.value)}
                      placeholder="Ex: álcool gel, papel A4, dipirona"
                      className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                    />
                    {dadosItem.objeto && (
                      <p className="text-xs text-green-600 mt-1">
                        ✅ Unidades sugeridas automaticamente baseadas no item
                      </p>
                    )}
                  </div>

                  <div className="grid grid-cols-2 gap-3">
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Quantidade: *
                      </label>
                      <input
                        type="number"
                        value={dadosItem.quantidade}
                        onChange={(e) => atualizarDadosItem('quantidade', e.target.value)}
                        placeholder="100"
                        className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium mb-2">
                        Unidade: *
                      </label>
                      <select
                        value={dadosItem.unidade}
                        onChange={(e) => atualizarDadosItem('unidade', e.target.value)}
                        className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                      >
                        <option value="">Selecione...</option>
                        {unidadesComuns.map(unidade => (
                          <option key={unidade} value={unidade}>{unidade}</option>
                        ))}
                      </select>
                    </div>
                  </div>
                </div>

                {/* COLUNA 2: ESPECIFICAÇÕES TÉCNICAS */}
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Volume/Tamanho:
                    </label>
                    <input
                      type="text"
                      value={dadosItem.volume}
                      onChange={(e) => atualizarDadosItem('volume', e.target.value)}
                      placeholder="Ex: 500ml, 1L, A4"
                      className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Concentração/Dosagem:
                    </label>
                    <input
                      type="text"
                      value={dadosItem.concentracao}
                      onChange={(e) => atualizarDadosItem('concentracao', e.target.value)}
                      placeholder="Ex: 70%, 46°, 500mg, 75g/m²"
                      className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Tipo/Estado:
                    </label>
                    <input
                      type="text"
                      value={dadosItem.tipo}
                      onChange={(e) => atualizarDadosItem('tipo', e.target.value)}
                      placeholder="Ex: gel, líquido, comprimido"
                      className="w-full p-3 border border-border rounded-md bg-background text-foreground"
                    />
                  </div>

                  <div>
                    <label className="block text-sm font-medium mb-2">
                      Especificação Técnica:
                    </label>
                    <textarea
                      value={dadosItem.especificacao}
                      onChange={(e) => atualizarDadosItem('especificacao', e.target.value)}
                      placeholder="Descreva as especificações técnicas detalhadas do item (concentração, marca, modelo, características especiais, etc.)"
                      className="w-full h-20 p-3 border border-border rounded-md resize-none bg-background text-foreground"
                    />
                  </div>
                </div>
              </div>

              {/* PREVIEW DA PESQUISA */}
              <div className="mt-6 p-4 bg-muted/50 rounded-lg">
                <h3 className="font-medium mb-2">📋 Preview da Pesquisa:</h3>
                <p className="text-sm">
                  <strong>Processo:</strong> {dadosItem.numeroProcesso || 'Não informado'}<br/>
                  <strong>Item:</strong> {dadosItem.objeto || 'Não informado'}
                  {dadosItem.volume && ` ${dadosItem.volume}`}
                  {dadosItem.concentracao && ` ${dadosItem.concentracao}`}
                  {dadosItem.tipo && ` (${dadosItem.tipo})`}<br/>
                  <strong>Quantidade:</strong> {dadosItem.quantidade || '0'} {dadosItem.unidade || 'UN'}<br/>
                  <strong>Busca PNCP:</strong> "{dadosItem.objeto} {dadosItem.volume} {dadosItem.concentracao} {dadosItem.tipo}".trim()
                </p>
              </div>

              <div className="flex space-x-3 mt-6">
                <Button
                  onClick={executarPesquisaManual}
                  className="flex-1"
                  disabled={!dadosItem.objeto || !dadosItem.quantidade || !dadosItem.unidade}
                >
                  <Search className="mr-2 h-4 w-4" />
                  Pesquisar no PNCP
                </Button>
                <Button
                  variant="outline"
                  onClick={() => setShowInsercaoManual(false)}
                >
                  Cancelar
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
