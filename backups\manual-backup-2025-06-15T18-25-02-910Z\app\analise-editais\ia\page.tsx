'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Textarea } from '@/components/ui/textarea';
import { 
  Brain, 
  MessageSquare, 
  Send, 
  Bot, 
  User,
  Lightbulb,
  FileText,
  Scale,
  AlertTriangle,
  CheckCircle,
  Zap
} from 'lucide-react';

interface Mensagem {
  id: string;
  tipo: 'user' | 'assistant';
  conteudo: string;
  timestamp: Date;
  categoria?: string;
}

export default function IAAssistantPage() {
  const [mensagens, setMensagens] = useState<Mensagem[]>([
    {
      id: '1',
      tipo: 'assistant',
      conteudo: 'Olá! Sou o assistente de IA especializado em análise de editais e Lei 14.133/21. Como posso ajudá-lo hoje?',
      timestamp: new Date(),
      categoria: 'saudacao'
    }
  ]);
  const [novaMensagem, setNovaMensagem] = useState('');
  const [loading, setLoading] = useState(false);

  const perguntasRapidas = [
    {
      titulo: 'Análise de Conformidade',
      pergunta: 'Como verificar se um edital está conforme a Lei 14.133/21?',
      categoria: 'conformidade'
    },
    {
      titulo: 'Decreto Municipal',
      pergunta: 'Quais são os requisitos do Decreto 9337/2024 para editais de Mauá?',
      categoria: 'decreto'
    },
    {
      titulo: 'Detecção de Riscos',
      pergunta: 'Que tipos de riscos a IA pode identificar em documentos?',
      categoria: 'riscos'
    },
    {
      titulo: 'TR no Edital',
      pergunta: 'Como o sistema detecta TR dentro do edital automaticamente?',
      categoria: 'tr'
    }
  ];

  const enviarMensagem = async (conteudo: string) => {
    if (!conteudo.trim()) return;

    const mensagemUser: Mensagem = {
      id: Date.now().toString(),
      tipo: 'user',
      conteudo: conteudo.trim(),
      timestamp: new Date()
    };

    setMensagens(prev => [...prev, mensagemUser]);
    setNovaMensagem('');
    setLoading(true);

    // Simular resposta da IA
    setTimeout(() => {
      const resposta = gerarRespostaIA(conteudo);
      const mensagemIA: Mensagem = {
        id: (Date.now() + 1).toString(),
        tipo: 'assistant',
        conteudo: resposta.conteudo,
        timestamp: new Date(),
        categoria: resposta.categoria
      };

      setMensagens(prev => [...prev, mensagemIA]);
      setLoading(false);
    }, 1500);
  };

  const gerarRespostaIA = (pergunta: string): { conteudo: string; categoria: string } => {
    const perguntaLower = pergunta.toLowerCase();

    if (perguntaLower.includes('conformidade') || perguntaLower.includes('lei 14.133')) {
      return {
        conteudo: `📋 **Análise de Conformidade Lei 14.133/21:**

**Elementos Obrigatórios do Edital (Art. 40):**
✅ Preâmbulo com identificação da administração
✅ Objeto claramente definido
✅ Critérios de julgamento
✅ Condições de habilitação
✅ Prazos e condições de execução

**Verificações Automáticas:**
🔍 Modalidade adequada ao valor/objeto
🔍 Fundamentação do interesse público
🔍 Pesquisa de preços atualizada
🔍 Especificações técnicas detalhadas

**Score de Conformidade:** O sistema calcula automaticamente baseado em 50+ critérios da lei.`,
        categoria: 'conformidade'
      };
    }

    if (perguntaLower.includes('decreto') || perguntaLower.includes('9337')) {
      return {
        conteudo: `🏛️ **Decreto Municipal 9337/2024 - Padrão Mauá:**

**Identificações Obrigatórias:**
✅ CNPJ: 46.643.466/0001-06
✅ Endereço: Av. João Ramalho, 205 - Centro
✅ Competência da CLMP claramente definida

**Estrutura Padrão:**
📄 Cabeçalho municipal padronizado
📄 Identificação da secretaria solicitante
📄 Responsável técnico nomeado
📄 Coerência com documentos municipais

**Verificação Automática:** O sistema valida todos os elementos do decreto automaticamente.`,
        categoria: 'decreto'
      };
    }

    if (perguntaLower.includes('risco') || perguntaLower.includes('detecta')) {
      return {
        conteudo: `🚨 **Detecção de Riscos com IA:**

**Tipos de Riscos Identificados:**
⚠️ **Conflitos de Objeto:** Medicamentos vs Ração Animal
⚠️ **Especificações Vagas:** Falta de detalhamento técnico
⚠️ **Prazos Inadequados:** Cronograma irrealista
⚠️ **Habilitação Restritiva:** Critérios excessivos

**Machine Learning:**
🧠 Análise de padrões em 1000+ editais
🧠 Detecção de anomalias textuais
🧠 Comparação com jurisprudência
🧠 Score de confiança 85-95%

**Alertas Automáticos:** Sistema gera alertas em tempo real para riscos críticos.`,
        categoria: 'riscos'
      };
    }

    if (perguntaLower.includes('tr') || perguntaLower.includes('termo') || perguntaLower.includes('anexo')) {
      return {
        conteudo: `📄 **Extração Automática de TR:**

**Como Funciona:**
🔍 **Busca por "Anexo I"** no texto do edital
🔍 **Análise de estrutura** típica de TR
🔍 **Validação de conteúdo** técnico
🔍 **Confiança 85%+** para extração

**Casos Especiais:**
🏗️ **Obras:** TR separado obrigatório
📦 **Materiais:** TR geralmente no edital
🔧 **Serviços:** Pode estar em ambos

**Vantagens:**
✅ Não bloqueia análise se TR não enviado
✅ Reduz trabalho manual
✅ Mantém precisão da análise
✅ Detecta inconsistências automaticamente`,
        categoria: 'tr'
      };
    }

    return {
      conteudo: `🤖 Entendi sua pergunta sobre "${pergunta}". 

Como assistente especializado em análise de editais, posso ajudar com:

📋 **Conformidade Legal** - Lei 14.133/21 e Decreto 9337/2024
🔍 **Análise de Riscos** - Detecção automática de problemas
📄 **Documentos** - ETP, Edital, TR e suas relações
⚖️ **Jurisprudência** - Padrões e boas práticas
🎯 **Otimização** - Sugestões de melhorias

Pode fazer uma pergunta mais específica ou usar as sugestões rápidas acima!`,
      categoria: 'geral'
    };
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">IA Assistant</h1>
          <p className="text-muted-foreground mt-2">
            Assistente inteligente especializado em análise de editais e conformidade legal
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <Brain className="mr-1 h-3 w-3" />
            IA Especializada
          </Badge>
          <Badge variant="outline" className="text-xs">
            <Scale className="mr-1 h-3 w-3" />
            Lei 14.133/21
          </Badge>
          <Badge variant="outline" className="text-xs">
            <FileText className="mr-1 h-3 w-3" />
            Decreto 9337/2024
          </Badge>
        </div>
      </div>

      {/* Perguntas Rápidas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Lightbulb className="mr-2 h-5 w-5" />
            Perguntas Rápidas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {perguntasRapidas.map((item, index) => (
              <Button
                key={index}
                variant="outline"
                className="h-auto p-4 text-left justify-start"
                onClick={() => enviarMensagem(item.pergunta)}
              >
                <div>
                  <div className="font-medium text-sm">{item.titulo}</div>
                  <div className="text-xs text-muted-foreground mt-1">{item.pergunta}</div>
                </div>
              </Button>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Chat */}
      <Card className="h-[600px] flex flex-col">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <MessageSquare className="mr-2 h-5 w-5" />
            Conversa com IA
          </CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col p-0">
          {/* Mensagens */}
          <div className="flex-1 overflow-y-auto p-4 space-y-4">
            {mensagens.map((mensagem) => (
              <div
                key={mensagem.id}
                className={`flex ${mensagem.tipo === 'user' ? 'justify-end' : 'justify-start'}`}
              >
                <div className={`flex items-start space-x-2 max-w-[80%] ${
                  mensagem.tipo === 'user' ? 'flex-row-reverse space-x-reverse' : ''
                }`}>
                  <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
                    mensagem.tipo === 'user' 
                      ? 'bg-primary text-primary-foreground' 
                      : 'bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300'
                  }`}>
                    {mensagem.tipo === 'user' ? <User size={16} /> : <Bot size={16} />}
                  </div>
                  <div className={`rounded-lg p-3 ${
                    mensagem.tipo === 'user'
                      ? 'bg-primary text-primary-foreground'
                      : 'bg-muted'
                  }`}>
                    <div className="text-sm whitespace-pre-wrap">{mensagem.conteudo}</div>
                    <div className="text-xs opacity-70 mt-1">
                      {mensagem.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                </div>
              </div>
            ))}
            {loading && (
              <div className="flex justify-start">
                <div className="flex items-start space-x-2">
                  <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900 text-blue-600 dark:text-blue-300 flex items-center justify-center">
                    <Bot size={16} />
                  </div>
                  <div className="bg-muted rounded-lg p-3">
                    <div className="flex space-x-1">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce"></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.1s'}}></div>
                      <div className="w-2 h-2 bg-muted-foreground rounded-full animate-bounce" style={{animationDelay: '0.2s'}}></div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </div>

          {/* Input */}
          <div className="border-t border-border p-4">
            <div className="flex space-x-2">
              <Textarea
                placeholder="Digite sua pergunta sobre análise de editais..."
                value={novaMensagem}
                onChange={(e) => setNovaMensagem(e.target.value)}
                className="flex-1 min-h-[40px] max-h-[120px]"
                onKeyDown={(e) => {
                  if (e.key === 'Enter' && !e.shiftKey) {
                    e.preventDefault();
                    enviarMensagem(novaMensagem);
                  }
                }}
              />
              <Button
                onClick={() => enviarMensagem(novaMensagem)}
                disabled={loading || !novaMensagem.trim()}
                size="icon"
              >
                <Send size={16} />
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
