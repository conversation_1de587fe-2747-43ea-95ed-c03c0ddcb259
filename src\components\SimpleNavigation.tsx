'use client';

import Link from 'next/link';
import { usePathname } from 'next/navigation';

export default function SimpleNavigation() {
  const pathname = usePathname();

  return (
    <header className="bg-blue-600 text-white">
      <div className="container mx-auto px-4">
        <div className="flex items-center justify-between h-16">
          <Link href="/dashboard" className="text-xl font-bold">
            InovaProcess
          </Link>
          
          <nav className="flex space-x-4">
            <Link 
              href="/dashboard" 
              className={`px-3 py-2 rounded ${pathname === '/dashboard' ? 'bg-blue-700' : 'hover:bg-blue-500'}`}
            >
              Dashboard
            </Link>
            <Link 
              href="/processos" 
              className={`px-3 py-2 rounded ${pathname === '/processos' || pathname.startsWith('/processos/') ? 'bg-blue-700' : 'hover:bg-blue-500'}`}
            >
              Processos
            </Link>
            <Link 
              href="/relatorios" 
              className={`px-3 py-2 rounded ${pathname === '/relatorios' || pathname.startsWith('/relatorios/') ? 'bg-blue-700' : 'hover:bg-blue-500'}`}
            >
              Relatórios
            </Link>
          </nav>
        </div>
      </div>
    </header>
  );
}
