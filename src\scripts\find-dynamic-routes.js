const fs = require('fs');
const path = require('path');

// Função para verificar se um diretório é uma rota dinâmica
function isDynamicRoute(dirName) {
  return dirName.startsWith('[') && dirName.endsWith(']');
}

// Função para listar todos os diretórios em um caminho
function listDirectories(dirPath) {
  if (!fs.existsSync(dirPath)) return [];
  
  return fs.readdirSync(dirPath, { withFileTypes: true })
    .filter(dirent => dirent.isDirectory())
    .map(dirent => dirent.name);
}

// Função para encontrar todas as rotas dinâmicas
function findDynamicRoutes(basePath, prefix = '') {
  const appDir = path.join(process.cwd(), basePath);
  let routes = [];
  
  // Verifica se o diretório existe
  if (!fs.existsSync(appDir)) return routes;
  
  // Lista todos os diretórios
  const dirs = listDirectories(appDir);
  
  for (const dir of dirs) {
    const fullPath = path.join(appDir, dir);
    const relativePath = prefix ? `${prefix}/${dir}` : dir;
    
    if (isDynamicRoute(dir)) {
      routes.push({
        path: relativePath,
        fullPath: fullPath
      });
    }
    
    // Recursivamente procura em subdiretórios
    const subRoutes = findDynamicRoutes(fullPath, relativePath);
    routes = routes.concat(subRoutes);
  }
  
  return routes;
}

// Encontra todas as rotas dinâmicas
const dynamicRoutes = findDynamicRoutes('src/app');

console.log('Rotas dinâmicas encontradas:');
dynamicRoutes.forEach(route => {
  console.log(`- ${route.path} (${route.fullPath})`);
});

// SISTEMA DE PROTEÇÃO ATIVO - NÃO REMOVE ROTAS AUTOMATICAMENTE
if (dynamicRoutes.length > 0) {
  console.log('\n🛡️ PROTEÇÃO ATIVA: Rotas dinâmicas detectadas mas NÃO serão removidas automaticamente');
  console.log('📋 Para remover manualmente, use o comando específico com confirmação');
  dynamicRoutes.forEach(route => {
    console.log(`🔍 Detectada: ${route.path}`);
  });
  console.log('✅ Sistema protegido contra remoção acidental de rotas.');
} else {
  console.log('✅ Nenhuma rota dinâmica encontrada.');
}