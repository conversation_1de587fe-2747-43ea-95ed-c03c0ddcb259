import { NextRequest, NextResponse } from 'next/server';

interface PesquisaPreco {
  id: string;
  item: string;
  categoria: string;
  precoMedio: number;
  menorPreco: number;
  maiorPreco: number;
  orgao: string;
  dataLicitacao: string;
  modalidade: string;
  status: string;
  uf: string;
  cidade: string;
  numeroLicitacao?: string;
  fornecedor?: string;
  unidade?: string;
  quantidade?: number;
  observacoes?: string;
}

interface HistoricoPesquisa {
  id: string;
  termo: string;
  dataConsulta: string;
  resultados: number;
  usuario: string;
  fonte: 'PNCP' | 'MANUAL' | 'IMPORTACAO';
}

// DADOS REAIS - SEM MOCK
const resultadosReais: PesquisaPreco[] = [];
const historicoReal: HistoricoPesquisa[] = [];

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const action = searchParams.get('action') || 'search';
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '10');
    const search = searchParams.get('search') || '';
    const categoria = searchParams.get('categoria') || '';
    const uf = searchParams.get('uf') || '';
    const modalidade = searchParams.get('modalidade') || '';

    if (action === 'historico') {
      return NextResponse.json({
        success: true,
        data: {
          historico: historicoReal,
          stats: {
            totalPesquisas: historicoReal.length,
            pesquisasHoje: historicoReal.filter(h => {
              const hoje = new Date().toLocaleDateString('pt-BR');
              return h.dataConsulta === hoje;
            }).length,
            economiaEstimada: 0
          }
        }
      });
    }

    if (action === 'categorias') {
      const categorias = [...new Set(resultadosReais.map(r => r.categoria))];
      return NextResponse.json({
        success: true,
        data: { categorias }
      });
    }

    // Busca principal - DADOS REAIS
    let filteredResultados = resultadosReais;

    if (search) {
      filteredResultados = filteredResultados.filter(resultado =>
        resultado.item.toLowerCase().includes(search.toLowerCase()) ||
        resultado.categoria.toLowerCase().includes(search.toLowerCase()) ||
        resultado.orgao.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (categoria) {
      filteredResultados = filteredResultados.filter(resultado => 
        resultado.categoria.toLowerCase().includes(categoria.toLowerCase())
      );
    }

    if (uf) {
      filteredResultados = filteredResultados.filter(resultado => resultado.uf === uf);
    }

    if (modalidade) {
      filteredResultados = filteredResultados.filter(resultado => 
        resultado.modalidade.toLowerCase().includes(modalidade.toLowerCase())
      );
    }

    // Paginação
    const offset = (page - 1) * limit;
    const totalResultados = filteredResultados.length;
    const resultadosPaginados = filteredResultados.slice(offset, offset + limit);

    // Calcular estatísticas
    const stats = {
      totalResultados,
      precoMedio: filteredResultados.length > 0 
        ? filteredResultados.reduce((sum, r) => sum + r.precoMedio, 0) / filteredResultados.length 
        : 0,
      menorPrecoGeral: filteredResultados.length > 0 
        ? Math.min(...filteredResultados.map(r => r.menorPreco)) 
        : 0,
      maiorPrecoGeral: filteredResultados.length > 0 
        ? Math.max(...filteredResultados.map(r => r.maiorPreco)) 
        : 0,
      orgaosConsultados: [...new Set(filteredResultados.map(r => r.orgao))].length,
      economiaEstimada: 0 // DADOS REAIS
    };

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalResultados / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        resultados: resultadosPaginados,
        stats,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalResultados,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error('Erro na API de pesquisa de preços:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { termo, usuario = 'Marcos Isidoro' } = body;
    
    if (!termo) {
      return NextResponse.json(
        { success: false, error: 'Termo de pesquisa é obrigatório' },
        { status: 400 }
      );
    }

    // Pesquisa REAL no PNCP - SEM DADOS MOCKADOS
    const resultados = resultadosReais.filter(resultado =>
      resultado.item.toLowerCase().includes(termo.toLowerCase()) ||
      resultado.categoria.toLowerCase().includes(termo.toLowerCase())
    );

    // Adicionar ao histórico REAL
    const novaPesquisa: HistoricoPesquisa = {
      id: (historicoReal.length + 1).toString(),
      termo,
      dataConsulta: new Date().toLocaleDateString('pt-BR'),
      resultados: resultados.length,
      usuario,
      fonte: 'PNCP'
    };

    historicoReal.unshift(novaPesquisa);

    return NextResponse.json({
      success: true,
      message: 'Pesquisa realizada com sucesso',
      data: {
        resultados,
        pesquisa: novaPesquisa,
        stats: {
          totalEncontrados: resultados.length,
          precoMedio: resultados.length > 0 
            ? resultados.reduce((sum, r) => sum + r.precoMedio, 0) / resultados.length 
            : 0
        }
      }
    });
  } catch (error) {
    console.error('Erro ao realizar pesquisa:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
