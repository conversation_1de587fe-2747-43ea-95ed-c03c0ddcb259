'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import Link from 'next/link';
import {
  Users,
  Plus,
  Search,
  Shield,
  Edit,
  Trash2,
  Eye,
  UserCheck,
  UserX,
  Building2,
  Mail,
  Phone,
  Calendar,
  Activity
} from 'lucide-react';

interface Usuario {
  id: string;
  nome: string;
  email: string;
  telefone: string;
  secretaria: string;
  cargo: string;
  perfil: 'admin' | 'gestor' | 'operador';
  status: 'ativo' | 'inativo';
  ultimoAcesso: string;
  dataCriacao: string;
  permissoes: string[];
}

// Mock data para demonstração
const mockUsuarios: Usuario[] = [
  {
    id: '1',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 99999-9999',
    secretaria: 'CLMP',
    cargo: 'Coordenador',
    perfil: 'admin',
    status: 'ativo',
    ultimoAcesso: '2025-01-07 14:30',
    dataCriacao: '2024-01-15',
    permissoes: ['processos', 'contratos', 'usuarios', 'relatorios', 'configuracoes']
  },
  {
    id: '2',
    nome: 'Ana Santos',
    email: '<EMAIL>',
    telefone: '(11) 98888-8888',
    secretaria: 'SSDAN',
    cargo: 'Analista',
    perfil: 'gestor',
    status: 'ativo',
    ultimoAcesso: '2025-01-07 13:45',
    dataCriacao: '2024-02-20',
    permissoes: ['processos', 'contratos', 'relatorios']
  },
  {
    id: '3',
    nome: 'Carlos Oliveira',
    email: '<EMAIL>',
    telefone: '(11) 97777-7777',
    secretaria: 'Saúde',
    cargo: 'Técnico',
    perfil: 'operador',
    status: 'ativo',
    ultimoAcesso: '2025-01-06 16:20',
    dataCriacao: '2024-03-10',
    permissoes: ['processos']
  },
  {
    id: '4',
    nome: 'Maria Costa',
    email: '<EMAIL>',
    telefone: '(11) 96666-6666',
    secretaria: 'Educação',
    cargo: 'Coordenadora',
    perfil: 'gestor',
    status: 'inativo',
    ultimoAcesso: '2024-12-20 10:15',
    dataCriacao: '2024-01-30',
    permissoes: ['processos', 'relatorios']
  }
];

export default function UsuariosPage() {
  const [usuarios, setUsuarios] = useState<Usuario[]>(mockUsuarios);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroStatus, setFiltroStatus] = useState<'todos' | 'ativo' | 'inativo'>('todos');
  const [filtroPerfil, setFiltroPerfil] = useState<'todos' | 'admin' | 'gestor' | 'operador'>('todos');

  const usuariosFiltrados = usuarios.filter(usuario => {
    const matchSearch = usuario.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                       usuario.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                       usuario.secretaria.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchStatus = filtroStatus === 'todos' || usuario.status === filtroStatus;
    const matchPerfil = filtroPerfil === 'todos' || usuario.perfil === filtroPerfil;
    
    return matchSearch && matchStatus && matchPerfil;
  });

  const getPerfilColor = (perfil: string) => {
    switch (perfil) {
      case 'admin': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'gestor': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'operador': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    return status === 'ativo' 
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
  };

  const estatisticas = {
    total: usuarios.length,
    ativos: usuarios.filter(u => u.status === 'ativo').length,
    inativos: usuarios.filter(u => u.status === 'inativo').length,
    admins: usuarios.filter(u => u.perfil === 'admin').length,
    gestores: usuarios.filter(u => u.perfil === 'gestor').length,
    operadores: usuarios.filter(u => u.perfil === 'operador').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestão de Usuários</h1>
          <p className="text-muted-foreground mt-2">
            Controle total de usuários, permissões e acessos ao sistema
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-xs">
            <Users className="mr-1 h-3 w-3" />
            {usuariosFiltrados.length} usuários
          </Badge>

          <Link href="/registro">
            <Button>
              <Plus className="mr-2 h-4 w-4" />
              Novo Usuário
            </Button>
          </Link>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{estatisticas.total}</p>
              </div>
              <Users className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Ativos</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.ativos}</p>
              </div>
              <UserCheck className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Inativos</p>
                <p className="text-2xl font-bold text-red-600">{estatisticas.inativos}</p>
              </div>
              <UserX className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Admins</p>
                <p className="text-2xl font-bold text-red-600">{estatisticas.admins}</p>
              </div>
              <Shield className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Gestores</p>
                <p className="text-2xl font-bold text-blue-600">{estatisticas.gestores}</p>
              </div>
              <Building2 className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Operadores</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.operadores}</p>
              </div>
              <Activity className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                type="text"
                placeholder="Buscar por nome, email ou secretaria..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={filtroStatus}
              onChange={(e) => setFiltroStatus(e.target.value as any)}
              className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
            >
              <option value="todos">Todos os Status</option>
              <option value="ativo">Ativos</option>
              <option value="inativo">Inativos</option>
            </select>

            <select
              value={filtroPerfil}
              onChange={(e) => setFiltroPerfil(e.target.value as any)}
              className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
            >
              <option value="todos">Todos os Perfis</option>
              <option value="admin">Administradores</option>
              <option value="gestor">Gestores</option>
              <option value="operador">Operadores</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Usuários */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
        {usuariosFiltrados.map((usuario) => (
          <Card key={usuario.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex items-start justify-between mb-4">
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 rounded-full bg-primary flex items-center justify-center text-primary-foreground font-bold">
                    {usuario.nome.split(' ').map(n => n[0]).join('').toUpperCase()}
                  </div>
                  <div>
                    <h3 className="font-semibold text-foreground">{usuario.nome}</h3>
                    <p className="text-sm text-muted-foreground">{usuario.cargo}</p>
                  </div>
                </div>
                
                <div className="flex items-center space-x-2">
                  <Badge className={getPerfilColor(usuario.perfil)}>
                    {usuario.perfil}
                  </Badge>
                  <Badge className={getStatusColor(usuario.status)}>
                    {usuario.status}
                  </Badge>
                </div>
              </div>

              <div className="space-y-2 mb-4">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="mr-2 h-4 w-4" />
                  {usuario.email}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Phone className="mr-2 h-4 w-4" />
                  {usuario.telefone}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Building2 className="mr-2 h-4 w-4" />
                  {usuario.secretaria}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Calendar className="mr-2 h-4 w-4" />
                  Último acesso: {usuario.ultimoAcesso}
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex flex-wrap gap-1">
                  {usuario.permissoes.slice(0, 3).map((permissao) => (
                    <Badge key={permissao} variant="outline" className="text-xs">
                      {permissao}
                    </Badge>
                  ))}
                  {usuario.permissoes.length > 3 && (
                    <Badge variant="outline" className="text-xs">
                      +{usuario.permissoes.length - 3}
                    </Badge>
                  )}
                </div>

                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm" className="text-red-600 hover:text-red-700">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {usuariosFiltrados.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Users className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhum usuário encontrado
            </h3>
            <p className="text-muted-foreground mb-4">
              Tente ajustar os filtros ou termos de busca.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
