'use client';

import { Toolt<PERSON>, Toolt<PERSON>Content, Tooltip<PERSON>rovider, TooltipTrigger } from '@/components/ui/tooltip';

interface MiniChartProps {
  data: Array<{ name: string; value: number; color?: string }>;
  type: 'sparkline' | 'mini-bar' | 'progress' | 'donut-mini';
  height?: number;
  showTooltip?: boolean;
}

export function MiniChart({ data, type, height = 40, showTooltip = true }: MiniChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="w-full bg-gray-100 dark:bg-gray-800 rounded h-2 animate-pulse" style={{ height }} />
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));
  const total = data.reduce((sum, item) => sum + item.value, 0);

  const defaultColors = [
    '#3B82F6', '#10B981', '#F59E0B', '#EF4444', 
    '#8B5CF6', '#06B6D4', '#84CC16', '#F97316'
  ];

  const renderSparkline = () => {
    const points = data.map((item, index) => {
      const x = (index / (data.length - 1)) * 100;
      const y = 100 - (item.value / maxValue) * 80;
      return `${x},${y}`;
    }).join(' ');

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="w-full cursor-pointer">
              <svg width="100%" height={height} viewBox="0 0 100 100" className="overflow-visible">
                <polyline
                  fill="none"
                  stroke="#3B82F6"
                  strokeWidth="2"
                  points={points}
                  className="hover:stroke-blue-600 transition-colors"
                />
                {data.map((item, index) => {
                  const x = (index / (data.length - 1)) * 100;
                  const y = 100 - (item.value / maxValue) * 80;
                  return (
                    <circle
                      key={index}
                      cx={x}
                      cy={y}
                      r="2"
                      fill="#3B82F6"
                      className="hover:r-3 transition-all"
                    />
                  );
                })}
              </svg>
            </div>
          </TooltipTrigger>
          {showTooltip && (
            <TooltipContent>
              <div className="space-y-1">
                {data.map((item, index) => (
                  <div key={index} className="flex justify-between text-xs">
                    <span>{item.name}:</span>
                    <span className="font-medium ml-2">{item.value}</span>
                  </div>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  const renderMiniBar = () => (
    <div className="flex items-end space-x-1 h-full">
      {data.slice(0, 8).map((item, index) => {
        const barHeight = (item.value / maxValue) * 100;
        const color = item.color || defaultColors[index % defaultColors.length];
        
        return (
          <TooltipProvider key={index}>
            <Tooltip>
              <TooltipTrigger asChild>
                <div
                  className="flex-1 rounded-t cursor-pointer hover:opacity-80 transition-opacity border border-gray-300 dark:border-gray-500"
                  style={{
                    height: `${Math.max(barHeight, 15)}%`,
                    backgroundColor: color,
                    minHeight: '8px'
                  }}
                />
              </TooltipTrigger>
              {showTooltip && (
                <TooltipContent>
                  <div className="text-center">
                    <p className="font-semibold text-xs">{item.name}</p>
                    <p className="text-xs">Valor: {item.value}</p>
                    <p className="text-xs">
                      {((item.value / total) * 100).toFixed(1)}%
                    </p>
                  </div>
                </TooltipContent>
              )}
            </Tooltip>
          </TooltipProvider>
        );
      })}
    </div>
  );

  const renderProgress = () => {
    const segments = data.slice(0, 5);
    let cumulativeWidth = 0;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="w-full bg-gray-300 dark:bg-gray-600 rounded-full overflow-hidden cursor-pointer border border-gray-400 dark:border-gray-500" style={{ height }}>
              <div className="flex h-full">
                {segments.map((item, index) => {
                  const width = (item.value / total) * 100;
                  const color = item.color || defaultColors[index % defaultColors.length];
                  cumulativeWidth += width;

                  return (
                    <div
                      key={index}
                      className="transition-all duration-500 hover:brightness-110"
                      style={{
                        width: `${width}%`,
                        backgroundColor: color,
                        minWidth: width > 0 ? '2px' : '0px'
                      }}
                    />
                  );
                })}
              </div>
            </div>
          </TooltipTrigger>
          {showTooltip && (
            <TooltipContent>
              <div className="space-y-1">
                {segments.map((item, index) => (
                  <div key={index} className="flex justify-between text-xs">
                    <div className="flex items-center">
                      <div 
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: item.color || defaultColors[index % defaultColors.length] }}
                      />
                      <span>{item.name}:</span>
                    </div>
                    <span className="font-medium ml-2">
                      {item.value} ({((item.value / total) * 100).toFixed(1)}%)
                    </span>
                  </div>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  const renderDonutMini = () => {
    const radius = 15;
    const circumference = 2 * Math.PI * radius;
    let cumulativePercentage = 0;

    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center justify-center cursor-pointer">
              <svg width={height} height={height} viewBox="0 0 40 40" className="transform -rotate-90">
                <circle
                  cx="20"
                  cy="20"
                  r={radius}
                  fill="transparent"
                  stroke="currentColor"
                  strokeWidth="4"
                  className="text-gray-300 dark:text-gray-600"
                />
                {data.slice(0, 4).map((item, index) => {
                  const percentage = (item.value / total) * 100;
                  const strokeDasharray = `${(percentage / 100) * circumference} ${circumference}`;
                  const strokeDashoffset = -((cumulativePercentage / 100) * circumference);
                  const color = item.color || defaultColors[index % defaultColors.length];
                  
                  cumulativePercentage += percentage;
                  
                  return (
                    <circle
                      key={index}
                      cx="20"
                      cy="20"
                      r={radius}
                      fill="transparent"
                      stroke={color}
                      strokeWidth="4"
                      strokeDasharray={strokeDasharray}
                      strokeDashoffset={strokeDashoffset}
                      className="hover:stroke-width-5 transition-all"
                    />
                  );
                })}
              </svg>
            </div>
          </TooltipTrigger>
          {showTooltip && (
            <TooltipContent>
              <div className="space-y-1">
                {data.slice(0, 4).map((item, index) => (
                  <div key={index} className="flex justify-between text-xs">
                    <div className="flex items-center">
                      <div 
                        className="w-2 h-2 rounded-full mr-2"
                        style={{ backgroundColor: item.color || defaultColors[index % defaultColors.length] }}
                      />
                      <span>{item.name}:</span>
                    </div>
                    <span className="font-medium ml-2">
                      {item.value} ({((item.value / total) * 100).toFixed(1)}%)
                    </span>
                  </div>
                ))}
              </div>
            </TooltipContent>
          )}
        </Tooltip>
      </TooltipProvider>
    );
  };

  switch (type) {
    case 'sparkline':
      return renderSparkline();
    case 'mini-bar':
      return renderMiniBar();
    case 'progress':
      return renderProgress();
    case 'donut-mini':
      return renderDonutMini();
    default:
      return renderProgress();
  }
}
