'use client';

import { Processo } from '@/types/processo';
import { calcularMetricasEficiencia } from '@/lib/processoUtils';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { 
  AlertTriangle, 
  Clock, 
  RefreshCw, 
  Shield, 
  TrendingUp, 
  CheckCircle,
  DollarSign,
  Target
} from 'lucide-react';

interface MetricasEficienciaCLMPProps {
  processos: Processo[];
}

export default function MetricasEficienciaCLMP({ processos }: MetricasEficienciaCLMPProps) {
  const metricas = calcularMetricasEficiencia(processos);
  
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center space-x-3">
        <Shield className="h-6 w-6 text-primary" />
        <div>
          <h2 className="text-2xl font-bold text-foreground">Métricas de Eficiência da CLMP</h2>
          <p className="text-muted-foreground">
            Demonstração da modernização e eficiência da gestão administrativa
          </p>
        </div>
      </div>

      {/* Cards de Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {/* Processos com Recursos Externos */}
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-blue-800 dark:text-blue-200 flex items-center">
              <DollarSign className="mr-2 h-4 w-4" />
              Recursos Externos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-300">{metricas.prioridadeAlta}</div>
            <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
              Gestão de fontes especiais
            </p>
            {metricas.prioridadeAlta > 0 && (
              <Badge variant="info" className="mt-2 text-xs">
                GESTÃO EFICIENTE
              </Badge>
            )}
          </CardContent>
        </Card>

        {/* Tempo Médio na CLMP */}
        <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-green-800 dark:text-green-200 flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Tempo na CLMP
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-300">{metricas.tempoMedioCLMP}</div>
            <p className="text-xs text-green-700 dark:text-green-400 mt-1">
              dias em média
            </p>
            <Badge variant="success" className="mt-2 text-xs">
              EFICIÊNCIA CLMP
            </Badge>
          </CardContent>
        </Card>

        {/* Tempo Médio na Secretaria */}
        <Card className="border-purple-200 bg-purple-50/50 dark:border-purple-800 dark:bg-purple-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-purple-800 dark:text-purple-200 flex items-center">
              <Clock className="mr-2 h-4 w-4" />
              Tempo na Secretaria
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-purple-600 dark:text-purple-300">{metricas.tempoMedioSecretaria}</div>
            <p className="text-xs text-purple-700 dark:text-purple-400 mt-1">
              dias antes da CLMP
            </p>
            <Badge variant="outline" className="mt-2 text-xs">
              ORIGEM
            </Badge>
          </CardContent>
        </Card>

        {/* Gargalos Críticos */}
        <Card className="border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-orange-800 dark:text-orange-200 flex items-center">
              <AlertTriangle className="mr-2 h-4 w-4" />
              Gargalos Críticos
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600 dark:text-orange-300">
              {metricas.processosNaSF + metricas.processosNaSAJ}
            </div>
            <div className="text-xs text-orange-700 dark:text-orange-400 mt-1 space-y-1">
              <div>SF: {metricas.processosNaSF} processos</div>
              <div>SAJ: {metricas.processosNaSAJ} processos</div>
            </div>
          </CardContent>
        </Card>

        {/* Retrabalho Real */}
        <Card className="border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-yellow-800 dark:text-yellow-200 flex items-center">
              <RefreshCw className="mr-2 h-4 w-4" />
              Retrabalho Real
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-yellow-600 dark:text-yellow-300">{metricas.retrabalhoReal}</div>
            <p className="text-xs text-yellow-700 dark:text-yellow-400 mt-1">
              Taxa: {metricas.taxaRetrabalho}%
            </p>
            <p className="text-xs text-yellow-600 dark:text-yellow-500 mt-1">
              Apenas adequações e retornos
            </p>
          </CardContent>
        </Card>

        {/* Taxa de Finalização */}
        <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-green-800 dark:text-green-200 flex items-center">
              <CheckCircle className="mr-2 h-4 w-4" />
              Taxa de Sucesso
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600 dark:text-green-300">{metricas.taxaFinalizacao}%</div>
            <p className="text-xs text-green-700 dark:text-green-400 mt-1">
              {metricas.finalizados} de {metricas.total} processos
            </p>
            <Badge variant="success" className="mt-2 text-xs">
              GANHO INSTITUCIONAL
            </Badge>
          </CardContent>
        </Card>

        {/* Eficiência CLMP */}
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
          <CardHeader className="pb-3">
            <CardTitle className="text-sm font-medium text-blue-800 dark:text-blue-200 flex items-center">
              <TrendingUp className="mr-2 h-4 w-4" />
              Eficiência CLMP
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600 dark:text-blue-300">{metricas.eficienciaCLMP}%</div>
            <p className="text-xs text-blue-700 dark:text-blue-400 mt-1">
              mais rápida que origem
            </p>
            <Badge variant="info" className="mt-2 text-xs">
              PERFORMANCE
            </Badge>
          </CardContent>
        </Card>
      </div>

      {/* Cards de Análise Detalhada */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Análise de Riscos */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <DollarSign className="mr-2 h-5 w-5 text-red-600" />
              Análise de Riscos Financeiros
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-red-50 dark:bg-red-950/30 rounded-lg border border-red-200 dark:border-red-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-red-800 dark:text-red-200">Processos em Risco</span>
                <Badge variant="destructive">{metricas.prioridadeAlta}</Badge>
              </div>
              <p className="text-xs text-red-700 dark:text-red-300">
                Processos com recursos de fontes externas (Estadual, Federal, Fundos, Convênios)
                que podem ser perdidos definitivamente se não licitados em tempo hábil.
              </p>
            </div>

            <div className="p-4 bg-blue-50 dark:bg-blue-950/30 rounded-lg border border-blue-200 dark:border-blue-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">Impacto Estratégico</span>
                <Badge variant="info">CRÍTICO</Badge>
              </div>
              <p className="text-xs text-blue-700 dark:text-blue-300">
                Perda de recursos resulta em bloqueio de futuros repasses e perda de oportunidades
                para o município.
              </p>
            </div>
          </CardContent>
        </Card>

        {/* Análise de Performance */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <Target className="mr-2 h-5 w-5 text-green-600" />
              Performance da CLMP
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="p-4 bg-green-50 dark:bg-green-950/30 rounded-lg border border-green-200 dark:border-green-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-green-800 dark:text-green-200">Taxa de Finalização</span>
                <Badge variant="success">{metricas.taxaFinalizacao}%</Badge>
              </div>
              <p className="text-xs text-green-700 dark:text-green-300">
                Percentual de processos finalizados com sucesso pela CLMP.
              </p>
            </div>

            <div className="p-4 bg-yellow-50 dark:bg-yellow-950/30 rounded-lg border border-yellow-200 dark:border-yellow-800">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">Taxa de Retrabalho</span>
                <Badge variant="warning">{metricas.taxaRetrabalho}%</Badge>
              </div>
              <p className="text-xs text-yellow-700 dark:text-yellow-300">
                Apenas processos que realmente retornaram para adequações ou foram
                encaminhados para secretarias por problemas.
              </p>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Resumo Executivo */}
      <Card className="border-primary/20 bg-primary/5">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="mr-2 h-5 w-5 text-primary" />
            Resumo Executivo de Performance
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Situação Atual</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Total de processos: {metricas.total}</li>
                <li>• Processos finalizados: {metricas.finalizados}</li>
                <li>• Taxa de sucesso: {metricas.taxaFinalizacao}%</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Riscos Identificados</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Risco de perda: {metricas.prioridadeAlta} processos</li>
                <li>• Gargalos críticos: {metricas.gargalosSF + metricas.gargalosSAJ}</li>
                <li>• Retrabalho real: {metricas.taxaRetrabalho}%</li>
              </ul>
            </div>
            
            <div className="space-y-2">
              <h4 className="font-semibold text-foreground">Demonstração de Eficiência</h4>
              <ul className="space-y-1 text-muted-foreground">
                <li>• Dados transparentes e precisos</li>
                <li>• Rastreamento temporal completo</li>
                <li>• Identificação proativa de melhorias</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
