'use client';

import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { 
  Shield, 
  Lock, 
  Eye, 
  FileText, 
  CheckCircle, 
  AlertTriangle,
  Download,
  ExternalLink,
  Server,
  Database,
  Globe,
  Users
} from 'lucide-react';

export default function SegurancaPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Segurança e Privacidade</h1>
          <p className="text-muted-foreground mt-2">
            Informações sobre proteção de dados e conformidade com a LGPD
          </p>
        </div>
        <Badge variant="outline" className="text-green-600 border-green-600">
          <CheckCircle className="w-4 h-4 mr-1" />
          Conforme LGPD
        </Badge>
      </div>

      {/* Status de Segurança */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Shield className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">SSL/TLS</p>
                <p className="text-xs text-green-600">Ativo</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Lock className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">Criptografia</p>
                <p className="text-xs text-green-600">AES-256</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Eye className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">Auditoria</p>
                <p className="text-xs text-green-600">24/7</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
          <CardContent className="p-4">
            <div className="flex items-center space-x-2">
              <Users className="w-5 h-5 text-green-600" />
              <div>
                <p className="text-sm font-medium text-green-800 dark:text-green-200">OAuth 2.0</p>
                <p className="text-xs text-green-600">Google</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Proteção de Dados */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="w-5 h-5 mr-2 text-blue-600" />
              Proteção de Dados
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Criptografia End-to-End</p>
                  <p className="text-xs text-muted-foreground">Dados sensíveis criptografados com AES-256</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Backup Seguro</p>
                  <p className="text-xs text-muted-foreground">Backups automáticos e criptografados</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Controle de Acesso</p>
                  <p className="text-xs text-muted-foreground">Permissões granulares por usuário</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Auditoria Completa</p>
                  <p className="text-xs text-muted-foreground">Log de todas as ações realizadas</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="w-5 h-5 mr-2 text-purple-600" />
              Conformidade LGPD
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-3">
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Consentimento Explícito</p>
                  <p className="text-xs text-muted-foreground">Coleta apenas dados necessários</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Direito ao Esquecimento</p>
                  <p className="text-xs text-muted-foreground">Exclusão de dados quando solicitado</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Portabilidade</p>
                  <p className="text-xs text-muted-foreground">Exportação de dados pessoais</p>
                </div>
              </div>
              
              <div className="flex items-start space-x-3">
                <CheckCircle className="w-4 h-4 text-green-600 mt-0.5" />
                <div>
                  <p className="text-sm font-medium">Notificação de Incidentes</p>
                  <p className="text-xs text-muted-foreground">Comunicação em até 72h</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Arquitetura de Segurança */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <Server className="w-5 h-5 mr-2 text-orange-600" />
            Arquitetura de Segurança
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center mx-auto">
                <Globe className="w-6 h-6 text-blue-600" />
              </div>
              <h3 className="font-semibold">Camada Web</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Firewall de Aplicação (WAF)</li>
                <li>• Certificado SSL/TLS</li>
                <li>• Rate Limiting</li>
                <li>• Headers de Segurança</li>
              </ul>
            </div>

            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center mx-auto">
                <Server className="w-6 h-6 text-green-600" />
              </div>
              <h3 className="font-semibold">Camada Aplicação</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Autenticação OAuth 2.0</li>
                <li>• Controle de Permissões</li>
                <li>• Validação de Entrada</li>
                <li>• Logs de Auditoria</li>
              </ul>
            </div>

            <div className="text-center space-y-3">
              <div className="w-12 h-12 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center mx-auto">
                <Database className="w-6 h-6 text-purple-600" />
              </div>
              <h3 className="font-semibold">Camada Dados</h3>
              <ul className="text-sm text-muted-foreground space-y-1">
                <li>• Criptografia AES-256</li>
                <li>• Backup Automático</li>
                <li>• Controle de Acesso</li>
                <li>• Monitoramento 24/7</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Documentos e Políticas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="w-5 h-5 mr-2 text-indigo-600" />
            Documentos e Políticas
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="flex items-center space-x-3">
                <Download className="w-5 h-5 text-blue-600" />
                <div className="text-left">
                  <p className="font-medium">Política de Privacidade</p>
                  <p className="text-sm text-muted-foreground">Conforme LGPD</p>
                </div>
              </div>
            </Button>

            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="flex items-center space-x-3">
                <Download className="w-5 h-5 text-green-600" />
                <div className="text-left">
                  <p className="font-medium">Termos de Uso</p>
                  <p className="text-sm text-muted-foreground">Condições de utilização</p>
                </div>
              </div>
            </Button>

            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="flex items-center space-x-3">
                <Download className="w-5 h-5 text-purple-600" />
                <div className="text-left">
                  <p className="font-medium">Manual de Segurança</p>
                  <p className="text-sm text-muted-foreground">Procedimentos e boas práticas</p>
                </div>
              </div>
            </Button>

            <Button variant="outline" className="justify-start h-auto p-4">
              <div className="flex items-center space-x-3">
                <ExternalLink className="w-5 h-5 text-orange-600" />
                <div className="text-left">
                  <p className="font-medium">Relatório de Auditoria</p>
                  <p className="text-sm text-muted-foreground">Última verificação</p>
                </div>
              </div>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Contato DPO */}
      <Card className="border-blue-200 bg-blue-50 dark:bg-blue-950/20">
        <CardHeader>
          <CardTitle className="flex items-center text-blue-800 dark:text-blue-200">
            <Users className="w-5 h-5 mr-2" />
            Encarregado de Dados (DPO)
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-3">
            <p className="text-sm text-blue-700 dark:text-blue-300">
              Para exercer seus direitos previstos na LGPD ou esclarecer dúvidas sobre tratamento de dados:
            </p>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
              <div>
                <p className="font-medium text-blue-800 dark:text-blue-200">E-mail:</p>
                <p className="text-blue-600"><EMAIL></p>
              </div>
              <div>
                <p className="font-medium text-blue-800 dark:text-blue-200">Telefone:</p>
                <p className="text-blue-600">(XX) XXXX-XXXX</p>
              </div>
            </div>

            <div className="pt-2">
              <p className="text-xs text-blue-600">
                Resposta em até 15 dias úteis conforme Art. 19 da LGPD
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Alerta de Segurança */}
      <Card className="border-yellow-200 bg-yellow-50 dark:bg-yellow-950/20">
        <CardContent className="p-4">
          <div className="flex items-start space-x-3">
            <AlertTriangle className="w-5 h-5 text-yellow-600 mt-0.5" />
            <div>
              <p className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Importante: Segurança é responsabilidade de todos
              </p>
              <p className="text-xs text-yellow-700 dark:text-yellow-300 mt-1">
                Mantenha suas credenciais seguras, não compartilhe senhas e reporte qualquer atividade suspeita imediatamente.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
