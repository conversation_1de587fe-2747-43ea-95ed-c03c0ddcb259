import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const files = formData.getAll('files') as File[];
    const categoria = formData.get('categoria') as string; // 'aprovado' | 'reprovado' | 'conflito'
    const secretaria = formData.get('secretaria') as string;
    const observacoes = formData.get('observacoes') as string;

    if (!files || files.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Nenhum arquivo enviado' },
        { status: 400 }
      );
    }

    // Criar diretório de treinamento se não existir
    const treinamentoDir = path.join(process.cwd(), 'data', 'treinamento-ml');
    const categoriaDir = path.join(treinamentoDir, categoria || 'nao_classificado');
    
    if (!existsSync(categoriaDir)) {
      await mkdir(categoriaDir, { recursive: true });
    }

    const resultados = [];

    for (const file of files) {
      try {
        // Gerar nome único para o arquivo
        const timestamp = Date.now();
        const extension = file.name.split('.').pop();
        const fileName = `${file.name.replace(/\.[^/.]+$/, "")}_${timestamp}.${extension}`;
        const filePath = path.join(categoriaDir, fileName);

        // Salvar arquivo
        const bytes = await file.arrayBuffer();
        const buffer = Buffer.from(bytes);
        await writeFile(filePath, buffer);

        // Extrair conteúdo e metadados
        const conteudoExtraido = await extrairConteudoTreinamento(buffer, extension || 'pdf');
        const metadados = await extrairMetadados(conteudoExtraido, file.name);

        // Detectar tipo com base no nome E conteúdo
        const tipoDetectado = detectarTipoDocumento(file.name, conteudoExtraido);

        // Salvar metadados em JSON
        const metadataPath = path.join(categoriaDir, `${fileName}.metadata.json`);
        const metadata = {
          nomeOriginal: file.name,
          nomeArquivo: fileName,
          categoria: categoria || 'nao_classificado',
          secretaria,
          observacoes,
          tamanho: file.size,
          dataUpload: new Date().toISOString(),
          tipo: tipoDetectado,
          objeto: metadados.objeto,
          palavrasChave: metadados.palavrasChave,
          estrutura: metadados.estrutura,
          problemas: metadados.problemasDetectados,
          deteccaoAutomatica: tipoDetectado !== 'outro'
        };

        await writeFile(metadataPath, JSON.stringify(metadata, null, 2));

        resultados.push({
          arquivo: fileName,
          status: 'sucesso',
          metadata
        });

      } catch (error) {
        console.error(`Erro ao processar ${file.name}:`, error);
        resultados.push({
          arquivo: file.name,
          status: 'erro',
          erro: 'Erro no processamento'
        });
      }
    }

    return NextResponse.json({
      success: true,
      data: {
        totalArquivos: files.length,
        processados: resultados.filter(r => r.status === 'sucesso').length,
        erros: resultados.filter(r => r.status === 'erro').length,
        resultados
      }
    });

  } catch (error) {
    console.error('Erro no upload em lote:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

async function extrairConteudoTreinamento(buffer: Buffer, extension: string): Promise<string> {
  // Simulação de extração de conteúdo para treinamento
  // Em produção, usar OCR/PDF parser real
  
  const simulatedContent = {
    pdf: `ESTUDO TÉCNICO PRELIMINAR
    
JUSTIFICATIVA
A presente contratação se justifica pela necessidade de...

OBJETO
Aquisição de medicamentos básicos para atendimento da população...

ANÁLISE DE RISCOS
Os principais riscos identificados são:
- Risco de desabastecimento
- Risco de qualidade dos produtos
- Risco de atraso na entrega

ESTIMATIVA DE CUSTOS
Valor estimado: R$ 150.000,00
Baseado em pesquisa de mercado realizada em...

CRONOGRAMA
Fase 1: Licitação (30 dias)
Fase 2: Contratação (15 dias)
Fase 3: Entrega (60 dias)`,

    doc: `EDITAL DE LICITAÇÃO
    
PREÂMBULO
A Prefeitura Municipal de Mauá, através da CLMP...

OBJETO
Contratação de empresa para fornecimento de...

CONDIÇÕES DE PARTICIPAÇÃO
Poderão participar desta licitação empresas...

CRITÉRIOS DE JULGAMENTO
O julgamento será pelo critério de menor preço...

DOCUMENTAÇÃO DE HABILITAÇÃO
- Habilitação jurídica
- Qualificação técnica
- Qualificação econômico-financeira
- Regularidade fiscal`,

    default: `TERMO DE REFERÊNCIA
    
ESPECIFICAÇÕES TÉCNICAS
O objeto deverá atender às seguintes especificações...

QUANTITATIVOS
Item 1: 1000 unidades
Item 2: 500 unidades

CRITÉRIOS DE ACEITAÇÃO
Os produtos serão aceitos mediante...

FISCALIZAÇÃO
A fiscalização será exercida por...`
  };

  return simulatedContent[extension as keyof typeof simulatedContent] || simulatedContent.default;
}

async function extrairMetadados(conteudo: string, nomeArquivo: string): Promise<any> {
  const conteudoLower = conteudo.toLowerCase();
  
  // Extrair objeto
  const objetoMatch = conteudo.match(/objeto[:\s]+([^.\n]+)/i);
  const objeto = objetoMatch ? objetoMatch[1].trim() : 'Não identificado';
  
  // Identificar palavras-chave por categoria
  const categorias = {
    medicamentos: ['medicamento', 'remédio', 'fármaco', 'saúde'],
    obras: ['obra', 'construção', 'pavimentação', 'reforma'],
    servicos: ['serviço', 'prestação', 'manutenção', 'consultoria'],
    materiais: ['material', 'equipamento', 'mobiliário'],
    alimentos: ['alimento', 'ração', 'merenda', 'nutrição']
  };
  
  const palavrasChave = [];
  for (const [categoria, palavras] of Object.entries(categorias)) {
    if (palavras.some(palavra => conteudoLower.includes(palavra))) {
      palavrasChave.push(categoria);
    }
  }
  
  // Analisar estrutura do documento
  const estrutura = {
    temJustificativa: conteudoLower.includes('justificativa'),
    temObjeto: conteudoLower.includes('objeto'),
    temRiscos: conteudoLower.includes('risco'),
    temCronograma: conteudoLower.includes('cronograma'),
    temEspecificacoes: conteudoLower.includes('especificação'),
    temHabilitacao: conteudoLower.includes('habilitação')
  };
  
  // Detectar problemas potenciais
  const problemasDetectados = [];
  if (!estrutura.temJustificativa && nomeArquivo.toLowerCase().includes('etp')) {
    problemasDetectados.push('ETP sem justificativa clara');
  }
  if (!estrutura.temHabilitacao && nomeArquivo.toLowerCase().includes('edital')) {
    problemasDetectados.push('Edital sem documentação de habilitação');
  }
  if (!estrutura.temEspecificacoes && nomeArquivo.toLowerCase().includes('tr')) {
    problemasDetectados.push('TR sem especificações técnicas');
  }
  
  return {
    objeto,
    palavrasChave,
    estrutura,
    problemasDetectados
  };
}

function detectarTipoDocumento(nomeArquivo: string, conteudo?: string): 'etp' | 'edital' | 'tr' | 'outro' {
  const nome = nomeArquivo.toLowerCase();
  const conteudoLower = conteudo?.toLowerCase() || '';

  // Detecção por nome do arquivo
  if (nome.includes('etp') || nome.includes('estudo')) return 'etp';
  if (nome.includes('edital')) return 'edital';
  if (nome.includes('tr') || nome.includes('termo')) return 'tr';

  // Detecção por conteúdo quando nome não é claro
  if (conteudo) {
    // Padrões para ETP
    const padroesETP = [
      'estudo técnico preliminar',
      'estudos técnicos preliminares',
      'justificativa da necessidade',
      'análise de riscos',
      'estimativa de custos'
    ];

    // Padrões para Edital
    const padroesEdital = [
      'edital de licitação',
      'pregão eletrônico',
      'concorrência pública',
      'tomada de preços',
      'convite',
      'preâmbulo',
      'condições de participação',
      'critérios de julgamento',
      'documentação de habilitação'
    ];

    // Padrões para TR
    const padroesTR = [
      'termo de referência',
      'especificações técnicas',
      'quantitativos',
      'critérios de aceitação',
      'fiscalização',
      'obrigações do contratado'
    ];

    // Contar ocorrências de cada tipo
    const scoreETP = padroesETP.filter(padrao => conteudoLower.includes(padrao)).length;
    const scoreEdital = padroesEdital.filter(padrao => conteudoLower.includes(padrao)).length;
    const scoreTR = padroesTR.filter(padrao => conteudoLower.includes(padrao)).length;

    // Retornar o tipo com maior score
    if (scoreETP > scoreEdital && scoreETP > scoreTR) return 'etp';
    if (scoreEdital > scoreETP && scoreEdital > scoreTR) return 'edital';
    if (scoreTR > scoreETP && scoreTR > scoreEdital) return 'tr';
  }

  return 'outro';
}
