'use client';

import { Processo } from '@/types/processo';
import { temFonteNaoTesouro, temGargaloCritico, temRetrabalhoReal, getFontesRecursos } from '@/lib/processoUtils';
import { Badge } from '@/components/ui/badge';
import { AlertTriangle, Clock, RefreshCw, DollarSign } from 'lucide-react';

interface AlertaPrioridadeProps {
  processo: Processo;
  showDetails?: boolean;
  compact?: boolean; // Nova prop para versão compacta
}

export default function AlertaPrioridade({ processo, showDetails = false, compact = false }: AlertaPrioridadeProps) {
  const fonteNaoTesouro = temFonteNaoTesouro(processo);
  const gargalo = temGargaloCritico(processo);
  const retrabalho = temRetrabalhoReal(processo);
  const fontes = getFontesRecursos(processo);
  
  // Se não há alertas, não renderiza nada
  if (!fonteNaoTesouro && !gargalo.tipo && !retrabalho) {
    return null;
  }

  // Versão compacta - apenas badges pequenos e discretos
  if (compact) {
    return (
      <div className="flex flex-wrap gap-1">
        {fonteNaoTesouro && (
          <Badge variant="info" className="text-xs px-2 py-0.5">
            💰 Externos
          </Badge>
        )}
        {gargalo.tipo && (
          <Badge variant="warning" className="text-xs px-2 py-0.5">
            ⏱️ {gargalo.tipo}
          </Badge>
        )}
        {retrabalho && (
          <Badge variant="outline" className="text-xs px-2 py-0.5">
            🔄 Retrabalho
          </Badge>
        )}
      </div>
    );
  }

  return (
    <div className="space-y-2">
      {/* Alerta de Prioridade Alta - Fontes não-Tesouro */}
      {fonteNaoTesouro && (
        <div className="flex items-center space-x-2 p-3 bg-red-50 border border-red-200 rounded-lg">
          <AlertTriangle className="h-5 w-5 text-red-600 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="destructive" className="text-xs font-bold">
                🔴 PRIORIDADE ALTA
              </Badge>
              <span className="text-sm font-medium text-red-800">
                RISCO DE PERDA DE RECURSO
              </span>
            </div>
            {showDetails && (
              <div className="mt-2 space-y-1">
                <p className="text-xs text-red-700">
                  Este processo possui recursos de fontes externas que podem ser perdidos se não licitado em tempo hábil.
                </p>
                <div className="flex flex-wrap gap-1">
                  {fontes
                    .filter(f => f.prioridade === 'ALTA')
                    .map((fonte, index) => (
                      <Badge key={index} variant="outline" className="text-xs border-red-300 text-red-700">
                        <DollarSign className="mr-1 h-3 w-3" />
                        {fonte.tipo}: {fonte.valor}
                      </Badge>
                    ))}
                </div>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Alerta de Gargalo Crítico */}
      {gargalo.tipo && (
        <div className="flex items-center space-x-2 p-3 bg-orange-50 border border-orange-200 rounded-lg">
          <Clock className="h-5 w-5 text-orange-600 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="warning" className="text-xs font-bold">
                ⏱️ GARGALO CRÍTICO
              </Badge>
              <span className="text-sm font-medium text-orange-800">
                {gargalo.tipo === 'SF' ? 'SECRETARIA DE FINANÇAS' : 'SECRETARIA DE ASSUNTOS JURÍDICOS'}
              </span>
            </div>
            {showDetails && (
              <div className="mt-2">
                <p className="text-xs text-orange-700">
                  Status: {gargalo.status}
                </p>
                <p className="text-xs text-orange-700">
                  {gargalo.tipo === 'SF' 
                    ? 'Processo aguardando análise orçamentária - pode impactar no tempo total.'
                    : 'Processo aguardando parecer jurídico - pode impactar no tempo total.'
                  }
                </p>
              </div>
            )}
          </div>
        </div>
      )}
      
      {/* Alerta de Retrabalho */}
      {retrabalho && (
        <div className="flex items-center space-x-2 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
          <RefreshCw className="h-5 w-5 text-yellow-600 flex-shrink-0" />
          <div className="flex-1">
            <div className="flex items-center space-x-2">
              <Badge variant="warning" className="text-xs font-bold">
                🔄 RETRABALHO
              </Badge>
              <span className="text-sm font-medium text-yellow-800">
                PROCESSO COM RETRABALHO
              </span>
            </div>
            {showDetails && (
              <div className="mt-2">
                <p className="text-xs text-yellow-700">
                  Status: {processo.STATUS}
                </p>
                <p className="text-xs text-yellow-700">
                  Processo retornou para adequações ou foi encaminhado para a secretaria.
                </p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}
