'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { 
  Clock, 
  TrendingUp, 
  TrendingDown, 
  Target,
  AlertTriangle,
  CheckCircle,
  Download,
  RefreshCw,
  BarChart3
} from 'lucide-react';

export default function RelatorioTempoMedioPage() {
  const [dados, setDados] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [periodo, setPeriodo] = useState('30');

  useEffect(() => {
    carregarDados();
  }, [periodo]);

  const carregarDados = async () => {
    setLoading(true);
    try {
      const response = await fetch(`/api/relatorios/tempo-medio?periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success) {
        setDados(data.data);
      }
    } catch (error) {
      console.error('Erro ao carregar dados:', error);
    } finally {
      setLoading(false);
    }
  };

  const exportarRelatorio = async (formato: 'pdf' | 'excel') => {
    try {
      const response = await fetch(`/api/relatorios/tempo-medio?formato=${formato}&periodo=${periodo}`);
      const data = await response.json();
      
      if (data.success && data.downloadUrl) {
        window.open(data.downloadUrl, '_blank');
      }
    } catch (error) {
      console.error('Erro ao exportar:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex items-center justify-center h-64">
          <RefreshCw className="h-8 w-8 animate-spin text-muted-foreground" />
        </div>
      </div>
    );
  }

  if (!dados) {
    return (
      <div className="space-y-6">
        <Card>
          <CardContent className="p-12 text-center">
            <AlertTriangle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium mb-2">Erro ao carregar dados</h3>
            <p className="text-muted-foreground mb-4">
              Não foi possível carregar os dados do relatório.
            </p>
            <Button onClick={carregarDados}>
              <RefreshCw className="mr-2 h-4 w-4" />
              Tentar Novamente
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Relatório de Tempo Médio</h1>
          <p className="text-muted-foreground mt-2">
            Análise de tempos de processamento e gargalos - {dados.periodo}
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <select
            value={periodo}
            onChange={(e) => setPeriodo(e.target.value)}
            className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
          >
            <option value="7">Últimos 7 dias</option>
            <option value="30">Últimos 30 dias</option>
            <option value="90">Últimos 90 dias</option>
            <option value="365">Último ano</option>
          </select>

          <Button variant="outline" onClick={() => exportarRelatorio('pdf')}>
            <Download className="mr-2 h-4 w-4" />
            PDF
          </Button>

          <Button variant="outline" onClick={() => exportarRelatorio('excel')}>
            <Download className="mr-2 h-4 w-4" />
            Excel
          </Button>

          <Button onClick={carregarDados}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Atualizar
          </Button>
        </div>
      </div>

      {/* Métricas Principais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Tempo Médio CLMP</p>
                <p className="text-3xl font-bold text-blue-600">{dados.resumo.tempoMedioCLMP} dias</p>
                <div className="flex items-center mt-1">
                  <Badge 
                    variant={dados.resumo.statusMeta === 'ATINGIDA' ? 'default' : 'destructive'}
                    className="text-xs"
                  >
                    Meta: {dados.resumo.metaTempo} dias
                  </Badge>
                </div>
              </div>
              <Clock className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Tempo Médio Geral</p>
                <p className="text-3xl font-bold text-orange-600">{dados.resumo.tempoMedioGeral} dias</p>
              </div>
              <BarChart3 className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Melhoria CLMP</p>
                <p className="text-3xl font-bold text-green-600">{dados.resumo.melhoriaPercentual}%</p>
                <p className="text-xs text-muted-foreground mt-1">vs. tempo geral</p>
              </div>
              <TrendingUp className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Status da Meta</p>
                <p className="text-lg font-bold">
                  {dados.resumo.statusMeta === 'ATINGIDA' ? 'Atingida' : 'Não Atingida'}
                </p>
              </div>
              {dados.resumo.statusMeta === 'ATINGIDA' ? (
                <CheckCircle className="h-8 w-8 text-green-600" />
              ) : (
                <AlertTriangle className="h-8 w-8 text-red-600" />
              )}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Distribuição de Tempo */}
      <Card>
        <CardHeader>
          <CardTitle>Distribuição de Tempo de Processamento</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            {Object.entries(dados.distribuicaoTempo).map(([faixa, quantidade]: [string, any]) => (
              <div key={faixa} className="text-center p-4 border border-border rounded-lg">
                <p className="text-2xl font-bold text-blue-600">{quantidade}</p>
                <p className="text-sm text-muted-foreground">{faixa}</p>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Tempo por Secretaria */}
      <Card>
        <CardHeader>
          <CardTitle>Tempo Médio por Secretaria</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {dados.temposPorSecretaria.map((secretaria: any, index: number) => (
              <div key={index} className="flex items-center justify-between p-4 border border-border rounded-lg">
                <div className="flex-1">
                  <h4 className="font-medium">{secretaria.nome}</h4>
                  <p className="text-sm text-muted-foreground">
                    {secretaria.processos} processos
                  </p>
                </div>
                <div className="text-right">
                  <p className="text-lg font-bold">{secretaria.tempoMedio.toFixed(1)} dias</p>
                  <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                    <div 
                      className="bg-blue-600 h-2 rounded-full" 
                      style={{ width: `${Math.min((secretaria.tempoMedio / 60) * 100, 100)}%` }}
                    ></div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Evolução Mensal e Gargalos */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle>Evolução Mensal</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dados.evolucaoMensal.map((item: any, index: number) => (
                <div key={index} className="flex items-center justify-between">
                  <span className="font-medium">{item.mes}</span>
                  <div className="flex items-center space-x-2">
                    <span className="font-bold">{item.tempo} dias</span>
                    {index > 0 && (
                      dados.evolucaoMensal[index - 1].tempo > item.tempo ? (
                        <TrendingDown className="h-4 w-4 text-green-600" />
                      ) : (
                        <TrendingUp className="h-4 w-4 text-red-600" />
                      )
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Gargalos por Etapa</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {dados.gargalosTempo.map((gargalo: any, index: number) => (
                <div key={index} className="flex items-center justify-between p-3 border border-border rounded-lg">
                  <div>
                    <h4 className="font-medium">{gargalo.etapa}</h4>
                    <p className="text-sm text-muted-foreground">
                      Meta: {gargalo.meta} dias
                    </p>
                  </div>
                  <div className="text-right">
                    <p className="font-bold">{gargalo.tempoMedio} dias</p>
                    <Badge variant={
                      gargalo.tempoMedio > gargalo.meta ? 'destructive' : 'default'
                    }>
                      {gargalo.tempoMedio > gargalo.meta ? 'Acima da Meta' : 'Dentro da Meta'}
                    </Badge>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
