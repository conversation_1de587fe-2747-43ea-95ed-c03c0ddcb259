import { NextRequest, NextResponse } from 'next/server';
import { analisarConformidadeLei14133 } from '@/lib/analise-editais/lei14133Analyzer';
import { analisarComIA } from '@/lib/analise-editais/mlAnalyzer';
import { gerarChecklist } from '@/lib/analise-editais/checklistGenerator';
import { analisarConformidadeDecreto9337 } from '@/lib/analise-editais/decretoMunicipal9337Analyzer';

interface DocumentoAnalise {
  tipo: 'etp' | 'edital' | 'tr';
  conteudo: string;
  fileName: string;
}

export async function POST(request: NextRequest) {
  try {
    const { documentos, trExtraido } = await request.json() as {
      documentos: DocumentoAnalise[];
      trExtraido?: any;
    };

    if (!documentos || documentos.length === 0) {
      return NextResponse.json(
        { success: false, error: 'Nenhum documento fornecido para análise' },
        { status: 400 }
      );
    }

    // Se TR foi extraído do edital, adicionar aos documentos
    if (trExtraido && trExtraido.encontrado) {
      const trDocumento: DocumentoAnalise = {
        tipo: 'tr',
        conteudo: trExtraido.conteudo,
        fileName: 'TR_extraido_do_edital.txt'
      };

      // Verificar se já não existe TR nos documentos
      const jaTemTR = documentos.some(doc => doc.tipo === 'tr');
      if (!jaTemTR) {
        documentos.push(trDocumento);
      }
    }

    // 1. Análise de conformidade com Lei 14.133/21
    const analiseConformidade = await analisarConformidadeLei14133(documentos);

    // 2. Análise de conformidade com Decreto Municipal 9337/2024
    const analiseDecreto9337 = await analisarConformidadeDecreto9337(documentos);

    // 3. Análise com Machine Learning
    const analiseIA = await analisarComIA(documentos);

    // 4. Gerar checklist baseado nas análises
    const checklist = await gerarChecklist(documentos, analiseConformidade, analiseIA, analiseDecreto9337);

    // 4. Calcular score geral
    const scoreGeral = calcularScoreGeral(checklist);

    // 5. Identificar riscos críticos (incluindo decreto municipal)
    const riscosCriticos = identificarRiscosCriticos(analiseConformidade, analiseIA, analiseDecreto9337);

    // 6. Gerar recomendações (incluindo decreto municipal)
    const recomendacoes = gerarRecomendacoes(analiseConformidade, analiseIA, analiseDecreto9337, scoreGeral);

    // 7. Determinar próximos passos
    const proximosPassos = determinarProximosPassos(scoreGeral, riscosCriticos, analiseConformidade);

    // 8. Gerar relatório de inconsistências
    const relatorioInconsistencias = gerarRelatorioInconsistencias(
      analiseConformidade,
      analiseIA,
      checklist,
      riscosCriticos
    );

    return NextResponse.json({
      success: true,
      data: {
        analiseId: `analise_${Date.now()}`,
        timestamp: new Date().toISOString(),
        scoreGeral,
        analiseConformidade,
        analiseDecreto9337,
        analiseIA,
        checklist,
        riscosCriticos,
        recomendacoes,
        proximosPassos,
        relatorioInconsistencias,
        status: scoreGeral >= 80 ? 'APROVADO' : scoreGeral >= 60 ? 'APROVADO_COM_RESSALVAS' : 'REPROVADO'
      }
    });

  } catch (error) {
    console.error('Erro na análise:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function calcularScoreGeral(checklist: any[]): number {
  const totalItens = checklist.length;
  const itensAtendidos = checklist.filter(item => item.atendido).length;
  const itensObrigatoriosNaoAtendidos = checklist.filter(
    item => item.obrigatorio && !item.atendido
  ).length;

  // Penalizar itens obrigatórios não atendidos
  const penalizacao = itensObrigatoriosNaoAtendidos * 15;
  const scoreBase = (itensAtendidos / totalItens) * 100;
  
  return Math.max(0, Math.round(scoreBase - penalizacao));
}

function identificarRiscosCriticos(analiseConformidade: any, analiseIA: any, analiseDecreto?: any): any[] {
  const riscos = [];

  // Riscos de conformidade legal Lei 14.133/21
  if (analiseConformidade.violacoesGraves?.length > 0) {
    riscos.push({
      tipo: 'LEGAL_CRITICO',
      nivel: 'ALTO',
      descricao: 'Violações graves da Lei 14.133/21 identificadas',
      detalhes: analiseConformidade.violacoesGraves,
      impacto: 'Processo pode ser anulado ou questionado juridicamente',
      origem: 'Lei 14.133/21'
    });
  }

  // Riscos do Decreto Municipal 9337/2024
  if (analiseDecreto?.violacoesGraves?.length > 0) {
    riscos.push({
      tipo: 'MUNICIPAL_CRITICO',
      nivel: 'ALTO',
      descricao: 'Violações do Decreto Municipal 9337/2024 identificadas',
      detalhes: analiseDecreto.violacoesGraves,
      impacto: 'Não conformidade com padrão municipal da Prefeitura de Mauá',
      origem: 'Decreto Municipal 9337/2024'
    });
  }

  // Riscos identificados pela IA
  if (analiseIA.riscosIdentificados?.length > 0) {
    analiseIA.riscosIdentificados.forEach((risco: any) => {
      if (risco.probabilidade > 0.7) {
        riscos.push({
          tipo: 'IA_DETECTADO',
          nivel: risco.probabilidade > 0.9 ? 'ALTO' : 'MEDIO',
          descricao: risco.descricao,
          detalhes: risco.evidencias,
          impacto: risco.impacto
        });
      }
    });
  }

  return riscos;
}

function gerarRecomendacoes(analiseConformidade: any, analiseIA: any, score: number): any[] {
  const recomendacoes = [];

  if (score < 60) {
    recomendacoes.push({
      prioridade: 'ALTA',
      categoria: 'GERAL',
      titulo: 'Revisão Geral Necessária',
      descricao: 'O processo apresenta múltiplas não conformidades que requerem revisão completa.',
      acoes: [
        'Revisar todos os documentos com base no checklist',
        'Consultar assessoria jurídica',
        'Realizar nova análise após correções'
      ]
    });
  }

  // Recomendações específicas da Lei 14.133/21
  if (analiseConformidade.recomendacoes?.length > 0) {
    analiseConformidade.recomendacoes.forEach((rec: any) => {
      recomendacoes.push({
        prioridade: rec.prioridade,
        categoria: 'LEGAL',
        titulo: rec.titulo,
        descricao: rec.descricao,
        acoes: rec.acoes,
        artigo: rec.artigo
      });
    });
  }

  // Recomendações da IA
  if (analiseIA.sugestoes?.length > 0) {
    analiseIA.sugestoes.forEach((sug: any) => {
      recomendacoes.push({
        prioridade: sug.prioridade,
        categoria: 'IA',
        titulo: sug.titulo,
        descricao: sug.descricao,
        acoes: sug.acoes,
        confianca: sug.confianca
      });
    });
  }

  return recomendacoes;
}

function determinarProximosPassos(score: number, riscosCriticos: any[], analiseConformidade: any): any {
  if (score >= 80 && riscosCriticos.length === 0) {
    return {
      acao: 'ENVIAR_PARA_SAJ',
      descricao: 'Processo aprovado - Enviar para SAJ para análise e parecer jurídico',
      prazo: '2 dias úteis',
      responsavel: 'CLMP',
      observacoes: 'Todas as verificações foram atendidas satisfatoriamente'
    };
  } else if (score >= 60) {
    return {
      acao: 'APROVADO_COM_RESSALVAS',
      descricao: 'Processo pode prosseguir com observações',
      prazo: '1 dia útil',
      responsavel: 'CLMP',
      observacoes: 'Atentar para as recomendações antes do envio para SAJ'
    };
  } else {
    return {
      acao: 'RETORNAR_PARA_SECRETARIA',
      descricao: 'Processo deve retornar para adequação pela secretaria',
      prazo: '15 dias úteis',
      responsavel: 'Secretaria Requisitante',
      observacoes: 'Corrigir todas as inconsistências apontadas no relatório'
    };
  }
}

function gerarRelatorioInconsistencias(
  analiseConformidade: any,
  analiseIA: any,
  checklist: any[],
  riscosCriticos: any[]
): any {
  const inconsistencias = [];

  // Violações da Lei 14.133/21
  if (analiseConformidade.violacoes?.length > 0) {
    inconsistencias.push({
      categoria: 'CONFORMIDADE LEGAL',
      titulo: 'Violações da Lei Federal 14.133/21',
      itens: analiseConformidade.violacoes.map((v: any) => ({
        artigo: v.artigo,
        descricao: v.descricao,
        gravidade: v.gravidade,
        evidencia: v.evidencia,
        correcao: v.sugestaoCorrecao
      }))
    });
  }

  // Riscos identificados pela IA
  if (riscosCriticos.length > 0) {
    inconsistencias.push({
      categoria: 'RISCOS CRÍTICOS',
      titulo: 'Riscos Identificados pela Análise Inteligente',
      itens: riscosCriticos.map((r: any) => ({
        tipo: r.tipo,
        descricao: r.descricao,
        nivel: r.nivel,
        impacto: r.impacto,
        detalhes: r.detalhes
      }))
    });
  }

  // Itens do checklist não atendidos
  const itensNaoAtendidos = checklist.filter(item => !item.atendido && item.obrigatorio);
  if (itensNaoAtendidos.length > 0) {
    inconsistencias.push({
      categoria: 'CHECKLIST OBRIGATÓRIO',
      titulo: 'Itens Obrigatórios Não Atendidos',
      itens: itensNaoAtendidos.map((item: any) => ({
        fonte: item.fonte,
        item: item.item,
        observacao: item.observacao,
        artigo: item.artigo
      }))
    });
  }

  // Falhas de formato (Padrão Mauá)
  const falhasFormato = analiseConformidade.violacoes?.filter((v: any) =>
    v.artigo.includes('Padrão Mauá')
  ) || [];

  if (falhasFormato.length > 0) {
    inconsistencias.push({
      categoria: 'FORMATO E PADRONIZAÇÃO',
      titulo: 'Não Conformidades com Padrão da Prefeitura de Mauá',
      itens: falhasFormato.map((f: any) => ({
        descricao: f.descricao,
        evidencia: f.evidencia,
        correcao: f.sugestaoCorrecao
      }))
    });
  }

  return {
    totalInconsistencias: inconsistencias.reduce((acc, cat) => acc + cat.itens.length, 0),
    categorias: inconsistencias,
    prazoCorrecao: '15 dias úteis',
    responsavel: 'Secretaria Requisitante',
    observacoes: [
      'Todas as inconsistências devem ser corrigidas antes da resubmissão',
      'Consultar assessoria jurídica para questões de conformidade legal',
      'Seguir rigorosamente o padrão da Prefeitura de Mauá',
      'Após correções, reenviar para nova análise da CLMP'
    ]
  };
}
