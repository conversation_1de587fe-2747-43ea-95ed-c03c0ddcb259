'use client';

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Building2,
  Plus,
  Search,
  Users,
  FileText,
  Clock,
  TrendingUp,
  Edit,
  Eye,
  Phone,
  Mail,
  MapPin,
  Calendar,
  Activity,
  AlertTriangle,
  CheckCircle
} from 'lucide-react';

interface Secretaria {
  id: string;
  nome: string;
  sigla: string;
  responsavel: string;
  email: string;
  telefone: string;
  endereco: string;
  status: 'ativa' | 'inativa';
  usuarios: number;
  processosAtivos: number;
  processosFinalizados: number;
  tempoMedioProcessamento: number;
  ultimaAtualizacao: string;
  dataCriacao: string;
  observacoes?: string;
}

// DADOS REAIS DO BANCO DE DADOS - SEM MOCK DATA
const loadSecretariasReais = async (): Promise<Secretaria[]> => {
  try {
    const response = await fetch('/api/secretarias');
    const data = await response.json();

    if (data.success) {
      return data.data.secretarias;
    }

    return [];
  } catch (error) {
    console.error('Erro ao carregar secretarias:', error);
    return [];
  }
};
  {
    id: '2',
    nome: 'Secretaria de Saúde, Desenvolvimento e Assistência Social',
    sigla: 'SSDAN',
    responsavel: 'Ana Santos',
    email: '<EMAIL>',
    telefone: '(11) 4512-8100',
    endereco: 'Rua Jundiaí, 63 - Centro',
    status: 'ativa',
    usuarios: 12,
    processosAtivos: 28,
    processosFinalizados: 95,
    tempoMedioProcessamento: 18,
    ultimaAtualizacao: '2025-01-07 13:45',
    dataCriacao: '2024-01-20'
  },
  {
    id: '3',
    nome: 'Secretaria de Saúde',
    sigla: 'Saúde',
    responsavel: 'Carlos Oliveira',
    email: '<EMAIL>',
    telefone: '(11) 4512-8200',
    endereco: 'Av. Barão de Mauá, 1000',
    status: 'ativa',
    usuarios: 8,
    processosAtivos: 35,
    processosFinalizados: 120,
    tempoMedioProcessamento: 15,
    ultimaAtualizacao: '2025-01-06 16:20',
    dataCriacao: '2024-02-01'
  },
  {
    id: '4',
    nome: 'Secretaria de Educação',
    sigla: 'Educação',
    responsavel: 'Maria Costa',
    email: '<EMAIL>',
    telefone: '(11) 4512-8300',
    endereco: 'Rua Cel. Silva Castro, 50',
    status: 'ativa',
    usuarios: 15,
    processosAtivos: 22,
    processosFinalizados: 88,
    tempoMedioProcessamento: 20,
    ultimaAtualizacao: '2025-01-05 10:15',
    dataCriacao: '2024-01-30'
  },
  {
    id: '5',
    nome: 'Secretaria de Obras e Mobilidade Urbana',
    sigla: 'SOMU',
    responsavel: 'João Pereira',
    email: '<EMAIL>',
    telefone: '(11) 4512-8400',
    endereco: 'Av. Dom José Gaspar, 300',
    status: 'ativa',
    usuarios: 6,
    processosAtivos: 18,
    processosFinalizados: 45,
    tempoMedioProcessamento: 25,
    ultimaAtualizacao: '2025-01-04 14:00',
    dataCriacao: '2024-03-15'
  }
];

export default function SecretariasPage() {
  const [secretarias, setSecretarias] = useState<Secretaria[]>(mockSecretarias);
  const [searchTerm, setSearchTerm] = useState('');
  const [filtroStatus, setFiltroStatus] = useState<'todas' | 'ativa' | 'inativa'>('todas');

  const secretariasFiltradas = secretarias.filter(secretaria => {
    const matchSearch = secretaria.nome.toLowerCase().includes(searchTerm.toLowerCase()) ||
                       secretaria.sigla.toLowerCase().includes(searchTerm.toLowerCase()) ||
                       secretaria.responsavel.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchStatus = filtroStatus === 'todas' || secretaria.status === filtroStatus;
    
    return matchSearch && matchStatus;
  });

  const getStatusColor = (status: string) => {
    return status === 'ativa' 
      ? 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
      : 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
  };

  const getPerformanceColor = (tempo: number) => {
    if (tempo <= 15) return 'text-green-600';
    if (tempo <= 20) return 'text-yellow-600';
    return 'text-red-600';
  };

  const estatisticas = {
    total: secretarias.length,
    ativas: secretarias.filter(s => s.status === 'ativa').length,
    totalUsuarios: secretarias.reduce((acc, s) => acc + s.usuarios, 0),
    totalProcessosAtivos: secretarias.reduce((acc, s) => acc + s.processosAtivos, 0),
    totalProcessosFinalizados: secretarias.reduce((acc, s) => acc + s.processosFinalizados, 0),
    tempoMedioGeral: Math.round(secretarias.reduce((acc, s) => acc + s.tempoMedioProcessamento, 0) / secretarias.length)
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Gestão de Secretarias</h1>
          <p className="text-muted-foreground mt-2">
            Controle e monitoramento de todas as secretarias e órgãos municipais
          </p>
        </div>

        <div className="flex items-center space-x-3">
          <Badge variant="outline" className="text-xs">
            <Building2 className="mr-1 h-3 w-3" />
            {secretariasFiltradas.length} secretarias
          </Badge>

          <Button>
            <Plus className="mr-2 h-4 w-4" />
            Nova Secretaria
          </Button>
        </div>
      </div>

      {/* Estatísticas Gerais */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-6 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{estatisticas.total}</p>
              </div>
              <Building2 className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Ativas</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.ativas}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Usuários</p>
                <p className="text-2xl font-bold text-blue-600">{estatisticas.totalUsuarios}</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Proc. Ativos</p>
                <p className="text-2xl font-bold text-orange-600">{estatisticas.totalProcessosAtivos}</p>
              </div>
              <Activity className="h-8 w-8 text-orange-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Finalizados</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.totalProcessosFinalizados}</p>
              </div>
              <FileText className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Tempo Médio</p>
                <p className={`text-2xl font-bold ${getPerformanceColor(estatisticas.tempoMedioGeral)}`}>
                  {estatisticas.tempoMedioGeral}d
                </p>
              </div>
              <Clock className="h-8 w-8 text-muted-foreground" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground" size={18} />
              <Input
                type="text"
                placeholder="Buscar por nome, sigla ou responsável..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="pl-10"
              />
            </div>

            <select
              value={filtroStatus}
              onChange={(e) => setFiltroStatus(e.target.value as any)}
              className="px-3 py-2 border border-input rounded-md bg-background text-foreground"
            >
              <option value="todas">Todas as Secretarias</option>
              <option value="ativa">Ativas</option>
              <option value="inativa">Inativas</option>
            </select>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Secretarias */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {secretariasFiltradas.map((secretaria) => (
          <Card key={secretaria.id} className="hover:shadow-md transition-shadow">
            <CardHeader className="pb-3">
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{secretaria.sigla}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">{secretaria.nome}</p>
                </div>
                <Badge className={getStatusColor(secretaria.status)}>
                  {secretaria.status}
                </Badge>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {/* Informações de Contato */}
              <div className="space-y-2">
                <div className="flex items-center text-sm text-muted-foreground">
                  <Users className="mr-2 h-4 w-4" />
                  Responsável: {secretaria.responsavel}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Mail className="mr-2 h-4 w-4" />
                  {secretaria.email}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <Phone className="mr-2 h-4 w-4" />
                  {secretaria.telefone}
                </div>
                <div className="flex items-center text-sm text-muted-foreground">
                  <MapPin className="mr-2 h-4 w-4" />
                  {secretaria.endereco}
                </div>
              </div>

              {/* Métricas de Performance */}
              <div className="grid grid-cols-2 gap-4 pt-4 border-t border-border">
                <div className="text-center">
                  <p className="text-2xl font-bold text-blue-600">{secretaria.usuarios}</p>
                  <p className="text-xs text-muted-foreground">Usuários</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-orange-600">{secretaria.processosAtivos}</p>
                  <p className="text-xs text-muted-foreground">Proc. Ativos</p>
                </div>
                <div className="text-center">
                  <p className="text-2xl font-bold text-green-600">{secretaria.processosFinalizados}</p>
                  <p className="text-xs text-muted-foreground">Finalizados</p>
                </div>
                <div className="text-center">
                  <p className={`text-2xl font-bold ${getPerformanceColor(secretaria.tempoMedioProcessamento)}`}>
                    {secretaria.tempoMedioProcessamento}d
                  </p>
                  <p className="text-xs text-muted-foreground">Tempo Médio</p>
                </div>
              </div>

              {/* Última Atualização */}
              <div className="flex items-center justify-between pt-4 border-t border-border">
                <div className="flex items-center text-xs text-muted-foreground">
                  <Calendar className="mr-1 h-3 w-3" />
                  Atualizado: {secretaria.ultimaAtualizacao}
                </div>

                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <Eye className="h-4 w-4" />
                  </Button>
                  <Button variant="outline" size="sm">
                    <Edit className="h-4 w-4" />
                  </Button>
                </div>
              </div>

              {/* Observações */}
              {secretaria.observacoes && (
                <div className="pt-2 border-t border-border">
                  <p className="text-xs text-muted-foreground italic">
                    {secretaria.observacoes}
                  </p>
                </div>
              )}
            </CardContent>
          </Card>
        ))}
      </div>

      {secretariasFiltradas.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <Building2 className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
            <h3 className="text-lg font-medium text-foreground mb-2">
              Nenhuma secretaria encontrada
            </h3>
            <p className="text-muted-foreground mb-4">
              Tente ajustar os filtros ou termos de busca.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
