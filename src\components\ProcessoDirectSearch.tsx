'use client';

import { useState, useEffect } from 'react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
// Removido import do Select - usando input com datalist
import { Badge } from '@/components/ui/badge';
import { Search, Hash, FileText, Building2, DollarSign, User, Filter } from 'lucide-react';

interface ProcessoDirectSearchProps {
  onSearch: (filters: any) => void;
  onShowAll: () => void;
  totalProcessos: number;
}

interface FilterOptions {
  responsaveis: string[];
  requisitantes: string[];
  fontes: string[];
}

export default function ProcessoDirectSearch({ onSearch, onShowAll, totalProcessos }: ProcessoDirectSearchProps) {
  const [filters, setFilters] = useState({
    numero: '',
    objeto: '',
    secretaria: '',
    fonte: '',
    responsavel: ''
  });

  const [filterOptions, setFilterOptions] = useState<FilterOptions>({
    responsaveis: [],
    requisitantes: [],
    fontes: []
  });

  const [loading, setLoading] = useState(false);

  // Carregar opções para dropdowns
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const response = await fetch('/api/processos/stats');
        const data = await response.json();
        
        if (data.success) {
          setFilterOptions({
            responsaveis: (data.data.filtros.responsaveis || []).sort(),
            requisitantes: (data.data.filtros.requisitantes || []).sort(),
            fontes: (data.data.filtros.fontes || []).sort()
          });
        }
      } catch (error) {
        console.error('Erro ao carregar opções de filtro:', error);
      }
    };

    loadFilterOptions();
  }, []);

  // Usar fontes reais da API ou fallback
  const fontesRecursos = filterOptions.fontes.length > 0
    ? filterOptions.fontes.map(fonte => ({ value: fonte, label: fonte }))
    : [
        { value: 'Fonte 0001 (TESOURO)', label: 'Fonte 0001 (TESOURO)' },
        { value: 'Fonte 0002 (ESTADUAL)', label: 'Fonte 0002 (ESTADUAL)' },
        { value: 'Fonte 0003 (FUNDO)', label: 'Fonte 0003 (FUNDO)' },
        { value: 'Fonte 0005 (FEDERAL)', label: 'Fonte 0005 (FEDERAL)' },
        { value: 'Fonte 0007 (FINISA)', label: 'Fonte 0007 (FINISA)' }
      ];

  const handleFilterChange = (field: string, value: string) => {
    const newFilters = { ...filters, [field]: value };
    setFilters(newFilters);

    // Busca instantânea apenas se houver algum valor preenchido
    const hasAnyFilter = Object.values(newFilters).some(v => v.trim() !== '');
    
    if (hasAnyFilter) {
      // Construir filtros para a API
      const searchFilters: any = {};
      
      if (newFilters.numero) {
        searchFilters.search = newFilters.numero;
      }
      if (newFilters.objeto) {
        searchFilters.search = newFilters.objeto;
      }
      if (newFilters.secretaria) {
        searchFilters.requisitante = newFilters.secretaria;
      }
      if (newFilters.responsavel) {
        searchFilters.responsavel = newFilters.responsavel;
      }
      
      onSearch(searchFilters);
    }
  };

  const handleClearFilters = () => {
    setFilters({
      numero: '',
      objeto: '',
      secretaria: '',
      fonte: '',
      responsavel: ''
    });
    // Limpar a página também
    onSearch({});
  };

  const handleShowAll = () => {
    handleClearFilters();
    onShowAll();
  };

  return (
    <Card>
      <CardContent className="p-6">
        {/* Botão Todos e contador */}
        <div className="flex items-center justify-between mb-6">
          <Button
            onClick={handleShowAll}
            variant="outline"
            className="flex items-center gap-2"
          >
            <Filter className="h-4 w-4" />
            Todos
            <Badge variant="secondary" className="ml-1">
              {totalProcessos}
            </Badge>
          </Button>

          <Button
            onClick={() => onSearch({ prioridadeGoverno: true })}
            variant="outline"
            className="flex items-center gap-2 bg-red-100 text-red-700 border-red-300 hover:bg-red-200 dark:bg-red-900/30 dark:text-red-300 dark:border-red-700 dark:hover:bg-red-900/50"
          >
            Prioridades Governo
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={handleClearFilters}
          >
            Limpar Filtros
          </Button>
        </div>

        {/* Campos de busca direta - Linha 1 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
          {/* Busca por Número */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Hash className="h-4 w-4" />
              Número (Processo/Certame/Contrato)
            </label>
            <Input
              placeholder="Ex: 9078, PE RP 006/2025, 013/2025"
              value={filters.numero}
              onChange={(e) => handleFilterChange('numero', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Busca por Objeto */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <FileText className="h-4 w-4" />
              Objeto
            </label>
            <Input
              placeholder="Ex: medicamentos, fraldas, material"
              value={filters.objeto}
              onChange={(e) => handleFilterChange('objeto', e.target.value)}
              className="w-full"
            />
          </div>

          {/* Busca por Secretaria */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <Building2 className="h-4 w-4" />
              Secretaria
            </label>
            <Input
              placeholder="Ex: SSDAN, SEMAM, SESAU"
              value={filters.secretaria}
              onChange={(e) => handleFilterChange('secretaria', e.target.value)}
              className="w-full"
            />
          </div>
        </div>

        {/* Campos de busca direta - Linha 2 */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Fonte de Recursos */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <DollarSign className="h-4 w-4" />
              Fonte de Recursos
            </label>
            <div className="relative">
              <Input
                placeholder="Selecione a fonte"
                value={filters.fonte}
                onChange={(e) => handleFilterChange('fonte', e.target.value)}
                list="fontes-list"
                className="w-full"
              />
              <datalist id="fontes-list">
                {fontesRecursos.map((fonte) => (
                  <option key={fonte.value} value={fonte.value}>
                    {fonte.label}
                  </option>
                ))}
              </datalist>
            </div>
          </div>

          {/* Responsável */}
          <div className="space-y-2">
            <label className="text-sm font-medium flex items-center gap-2">
              <User className="h-4 w-4" />
              Responsável
            </label>
            <div className="relative">
              <Input
                placeholder="Selecione o responsável"
                value={filters.responsavel}
                onChange={(e) => handleFilterChange('responsavel', e.target.value)}
                list="responsaveis-list"
                className="w-full"
              />
              <datalist id="responsaveis-list">
                {filterOptions.responsaveis.map((responsavel) => (
                  <option key={responsavel} value={responsavel}>
                    {responsavel}
                  </option>
                ))}
              </datalist>
            </div>
          </div>
        </div>

        {/* Dicas de busca */}
        <div className="mt-6 text-sm text-muted-foreground">
          <p className="font-medium mb-2">💡 Dicas de busca:</p>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-xs">
            <div>
              <strong>Número:</strong> Digite apenas números ou códigos (ex: 9078, PE RP 006)
            </div>
            <div>
              <strong>Objeto:</strong> Palavras-chave do que está sendo comprado
            </div>
            <div>
              <strong>Secretaria:</strong> Use siglas (SSDAN, SEMAM) ou nomes
            </div>
            <div>
              <strong>Busca instantânea:</strong> Resultados aparecem conforme você digita
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
