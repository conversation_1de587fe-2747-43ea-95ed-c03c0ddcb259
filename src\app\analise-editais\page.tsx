'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  FileText,
  Upload,
  CheckCircle,
  XCircle,
  Send,
  RefreshCw,
  Download,
  Eye,
  Brain,
  Scale,
  AlertTriangle,
  TrendingUp,
  FileCheck,
  Zap,
  Search
} from 'lucide-react';
import DistribuirPesquisa from '@/components/DistribuirPesquisa';

interface VersaoDocumento {
  versao: number;
  arquivo: File;
  dataUpload: Date;
  observacoes: string;
  scoreAnterior?: number;
}

interface ProblemaIdentificado {
  id: string;
  descricao: string;
  categoria: string;
  documentoAfetado: 'etp' | 'edital' | 'tr';
  primeiraDeteccao: number; // Número do retorno onde foi detectado pela primeira vez
  status: 'pendente' | 'corrigido' | 'nao_aplicavel';
}

interface HistoricoProcesso {
  numeroProcesso: string;
  versoes: {
    etp: VersaoDocumento[];
    edital: VersaoDocumento[];
    tr: VersaoDocumento[];
  };
  retornos: {
    numero: number;
    data: Date;
    motivo: string;
    problemas: string[];
    scoreAnterior: number;
    scoreAtual?: number;
    problemasIdentificados: ProblemaIdentificado[]; // Problemas detectados neste retorno
    documentosAlterados: ('etp' | 'edital' | 'tr')[]; // Quais documentos foram alterados
  }[];
  problemasHistorico: ProblemaIdentificado[]; // Todos os problemas já identificados
  statusAtual: 'primeira_analise' | 'aguardando_ajustes' | 'reanalise' | 'aprovado' | 'reprovado_final';
}

export default function AnaliseEditaisPage() {
  const [loading, setLoading] = useState(false);
  const [documentosUpload, setDocumentosUpload] = useState<{[key: string]: File | null}>({
    etp: null,
    edital: null,
    tr: null
  });
  const [documentosRevisados, setDocumentosRevisados] = useState<{[key: string]: File | null}>({
    etp: null,
    edital: null,
    tr: null
  });
  const [analiseRealizada, setAnaliseRealizada] = useState(false);
  const [numeroProcesso, setNumeroProcesso] = useState('');
  const [historicoProcesso, setHistoricoProcesso] = useState<HistoricoProcesso | null>(null);
  const [modoReanalise, setModoReanalise] = useState(false);
  const [observacoesAjuste, setObservacoesAjuste] = useState('');
  const [mostrarDistribuicao, setMostrarDistribuicao] = useState(false);

  const handleFileUpload = (tipo: string, file: File, isRevisado = false) => {
    if (isRevisado) {
      setDocumentosRevisados(prev => ({
        ...prev,
        [tipo]: file
      }));
    } else {
      setDocumentosUpload(prev => ({
        ...prev,
        [tipo]: file
      }));
    }
  };

  const buscarHistoricoProcesso = async (numero: string) => {
    // Simular busca no backend
    if (numero.includes('CC 045/2024')) {
      const historico: HistoricoProcesso = {
        numeroProcesso: numero,
        versoes: {
          etp: [
            {
              versao: 1,
              arquivo: new File([''], 'ETP_v1.pdf'),
              dataUpload: new Date('2024-12-15'),
              observacoes: 'Versão inicial',
              scoreAnterior: 65
            }
          ],
          edital: [
            {
              versao: 1,
              arquivo: new File([''], 'Edital_v1.pdf'),
              dataUpload: new Date('2024-12-15'),
              observacoes: 'Versão inicial',
              scoreAnterior: 70
            }
          ],
          tr: []
        },
        retornos: [
          {
            numero: 1,
            data: new Date('2024-12-16'),
            motivo: 'Conflito de objetos detectado - Análise inicial',
            problemas: [
              'ETP menciona medicamentos mas Edital refere-se a ração animal',
              'Especificações técnicas inadequadas',
              'Falta de detalhamento no TR'
            ],
            scoreAnterior: 65,
            problemasIdentificados: [
              {
                id: 'prob_001',
                descricao: 'ETP menciona medicamentos mas Edital refere-se a ração animal',
                categoria: 'Conflito de Objeto',
                documentoAfetado: 'etp',
                primeiraDeteccao: 1,
                status: 'pendente'
              },
              {
                id: 'prob_002',
                descricao: 'Especificações técnicas inadequadas',
                categoria: 'Especificação Técnica',
                documentoAfetado: 'tr',
                primeiraDeteccao: 1,
                status: 'pendente'
              }
            ],
            documentosAlterados: []
          },
          {
            numero: 2,
            data: new Date('2024-12-20'),
            motivo: 'Problemas anteriores persistem - Documentos não foram alterados adequadamente',
            problemas: [
              'ETP ainda menciona medicamentos (problema já identificado no retorno #1)',
              'Especificações técnicas ainda inadequadas (problema já identificado no retorno #1)'
            ],
            scoreAnterior: 72,
            problemasIdentificados: [], // Nenhum problema NOVO identificado
            documentosAlterados: ['etp'] // Apenas ETP foi alterado, mas inadequadamente
          }
        ],
        problemasHistorico: [
          {
            id: 'prob_001',
            descricao: 'ETP menciona medicamentos mas Edital refere-se a ração animal',
            categoria: 'Conflito de Objeto',
            documentoAfetado: 'etp',
            primeiraDeteccao: 1,
            status: 'pendente'
          },
          {
            id: 'prob_002',
            descricao: 'Especificações técnicas inadequadas',
            categoria: 'Especificação Técnica',
            documentoAfetado: 'tr',
            primeiraDeteccao: 1,
            status: 'pendente'
          }
        ],
        statusAtual: 'aguardando_ajustes'
      };
      setHistoricoProcesso(historico);
      setModoReanalise(true);
    } else {
      setHistoricoProcesso(null);
      setModoReanalise(false);
    }
  };

  const handleAnalise = async () => {
    setLoading(true);
    try {
      // Simular análise
      await new Promise(resolve => setTimeout(resolve, 3000));
      setAnaliseRealizada(true);

      // Se é reanalise, adicionar ao histórico
      if (modoReanalise && historicoProcesso) {
        // Verificar quais documentos foram alterados (simulação)
        const documentosAlterados: ('etp' | 'edital' | 'tr')[] = [];
        if (documentosRevisados.etp) documentosAlterados.push('etp');
        if (documentosRevisados.edital) documentosAlterados.push('edital');
        if (documentosRevisados.tr) documentosAlterados.push('tr');

        // Simular análise: se documentos foram alterados adequadamente, problemas são corrigidos
        const problemasCorrigidos = documentosAlterados.length > 0;

        const novoRetorno = {
          numero: historicoProcesso.retornos.length + 1,
          data: new Date(),
          motivo: problemasCorrigidos
            ? 'Documentos corrigidos adequadamente - Problemas resolvidos'
            : 'Documentos ainda não foram alterados ou alterações insuficientes',
          problemas: problemasCorrigidos
            ? ['✅ Todos os problemas anteriores foram corrigidos']
            : ['❌ Problemas anteriores persistem - documentos não foram alterados adequadamente'],
          scoreAnterior: 85,
          scoreAtual: problemasCorrigidos ? 92 : 78,
          problemasIdentificados: [], // IMPORTANTE: Não identificamos problemas NOVOS em reanalise
          documentosAlterados
        };

        setHistoricoProcesso(prev => prev ? {
          ...prev,
          retornos: [...prev.retornos, novoRetorno],
          statusAtual: problemasCorrigidos ? 'aprovado' : 'aguardando_ajustes'
        } : null);
      }
    } catch (error) {
      console.error('Erro na análise:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Análise de Editais</h1>
          <div className="text-left text-muted-foreground mt-2">
            <p>Sistema automatizado de checagem de conformidade</p>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-2 mt-4 sm:mt-0">
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <Brain className="mr-1 h-3 w-3" />
              IA + Machine Learning
            </Badge>
            <Badge variant="outline" className="text-xs">
              <Scale className="mr-1 h-3 w-3" />
              Lei 14.133/21
            </Badge>
          </div>
          <div className="flex items-center space-x-2">
            <Badge variant="outline" className="text-xs">
              <FileCheck className="mr-1 h-3 w-3" />
              Decreto 9337/2024
            </Badge>
            <Badge variant="outline" className="text-xs">
              <FileCheck className="mr-1 h-3 w-3" />
              Padrão Mauá
            </Badge>
          </div>
        </div>
      </div>

      {/* Legenda de Status */}
      <Card className="border-gray-200 bg-gray-50/50 dark:border-gray-700 dark:bg-gray-900/50">
        <CardContent className="p-4">
          <div className="flex flex-wrap items-center justify-center gap-6 text-sm">
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-green-500"></div>
              <span>Aprovado</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-red-500"></div>
              <span>Aguardando Ajustes</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-yellow-500"></div>
              <span>Em Análise</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-blue-500"></div>
              <span>Documentos Revisados</span>
            </div>
            <div className="flex items-center gap-2">
              <div className="w-3 h-3 rounded-full bg-orange-500"></div>
              <span>Reanalise</span>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Busca de Processo para Reanalise */}
      <Card className="border-orange-200 bg-orange-50/50 dark:border-orange-800 dark:bg-orange-950/50">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Search className="mr-2 h-5 w-5" />
            Buscar Processo para Reanalise
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-3">
            <Input
              placeholder="Digite o número do processo (ex: CC 045/2024)"
              value={numeroProcesso}
              onChange={(e) => setNumeroProcesso(e.target.value)}
              className="flex-1"
            />
            <Button
              onClick={() => buscarHistoricoProcesso(numeroProcesso)}
              disabled={!numeroProcesso.trim()}
            >
              <Search className="mr-2 h-4 w-4" />
              Buscar
            </Button>
          </div>

          {historicoProcesso && (
            <div className="mt-4 p-4 bg-white dark:bg-gray-900 rounded-lg border">
              <div className="flex items-center justify-between mb-3">
                <h4 className="font-semibold">📋 {historicoProcesso.numeroProcesso}</h4>
                <Badge variant={
                  historicoProcesso.statusAtual === 'aprovado' ? 'default' :
                  historicoProcesso.statusAtual === 'aguardando_ajustes' ? 'destructive' : 'secondary'
                }>
                  {historicoProcesso.statusAtual.replace('_', ' ').toUpperCase()}
                </Badge>
              </div>

              <div className="space-y-2">
                <p className="text-sm text-muted-foreground">
                  <strong>Retornos para ajuste:</strong> {historicoProcesso.retornos.length}
                </p>

                {historicoProcesso.retornos.length > 0 && (
                  <div className="space-y-2">
                    <p className="text-sm font-medium">Histórico de Retornos:</p>
                    {historicoProcesso.retornos.map((retorno, index) => (
                      <div key={index} className="text-xs p-2 bg-red-50 dark:bg-red-950/30 rounded border border-red-200">
                        <div className="flex justify-between items-start mb-1">
                          <span className="font-medium">Retorno #{retorno.numero}</span>
                          <span className="text-muted-foreground">{retorno.data.toLocaleDateString('pt-BR')}</span>
                        </div>
                        <p className="text-red-700 dark:text-red-300 mb-1">{retorno.motivo}</p>

                        {/* Mostrar problemas com indicação de quando foram detectados */}
                        <div className="space-y-1">
                          {retorno.problemas.map((problema, i) => (
                            <p key={i} className="text-red-600 dark:text-red-400">• {problema}</p>
                          ))}
                        </div>

                        {/* Indicar documentos alterados */}
                        {retorno.documentosAlterados && retorno.documentosAlterados.length > 0 && (
                          <div className="mt-2 p-1 bg-blue-100 dark:bg-blue-900/30 rounded">
                            <p className="text-blue-700 dark:text-blue-300 text-xs">
                              📄 Documentos alterados: {retorno.documentosAlterados.join(', ').toUpperCase()}
                            </p>
                          </div>
                        )}

                        {/* Mostrar se foram identificados problemas novos */}
                        {retorno.problemasIdentificados && retorno.problemasIdentificados.length > 0 ? (
                          <div className="mt-2 p-1 bg-orange-100 dark:bg-orange-900/30 rounded">
                            <p className="text-orange-700 dark:text-orange-300 text-xs">
                              🔍 {retorno.problemasIdentificados.length} problema(s) NOVO(S) identificado(s)
                            </p>
                          </div>
                        ) : retorno.numero > 1 && (
                          <div className="mt-2 p-1 bg-green-100 dark:bg-green-900/30 rounded">
                            <p className="text-green-700 dark:text-green-300 text-xs">
                              ✅ Nenhum problema NOVO - Apenas problemas já identificados anteriormente
                            </p>
                          </div>
                        )}

                        <p className="text-xs text-muted-foreground mt-1">
                          Score anterior: {retorno.scoreAnterior}%
                          {retorno.scoreAtual && ` → ${retorno.scoreAtual}%`}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Upload das Peças Originais */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {(['etp', 'edital', 'tr'] as const).map((tipo) => (
          <Card key={tipo}>
            <CardHeader>
              <CardTitle className="flex items-center text-lg">
                <Upload className="mr-2 h-5 w-5" />
                {tipo.toUpperCase()} Original
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <input
                  type="file"
                  id={`upload-${tipo}`}
                  accept=".pdf,.doc,.docx,.odt"
                  className="hidden"
                  onChange={(e) => {
                    const file = e.target.files?.[0];
                    if (file) handleFileUpload(tipo, file);
                  }}
                />
                <Button
                  variant="outline"
                  className="w-full"
                  disabled={loading}
                  onClick={() => document.getElementById(`upload-${tipo}`)?.click()}
                >
                  <Upload className="mr-2 h-4 w-4" />
                  Fazer Upload
                </Button>

                {/* Recomendação de formato */}
                <div className="text-center space-y-1">
                  <p className="text-xs text-green-600 dark:text-green-400 font-medium">
                    📄 PDF RECOMENDADO (melhor performance)
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Aceitos: PDF, DOC, DOCX, ODT
                  </p>
                </div>

                {documentosUpload[tipo] && (
                  <div className="text-xs text-green-600 dark:text-green-400 p-2 bg-green-50 dark:bg-green-950/30 rounded border">
                    ✅ {documentosUpload[tipo]?.name}
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Alerta de Consistência de Análise */}
      {modoReanalise && historicoProcesso && (
        <Card className="border-yellow-200 bg-yellow-50/50 dark:border-yellow-800 dark:bg-yellow-950/50">
          <CardContent className="p-4">
            <div className="flex items-start space-x-3">
              <AlertTriangle className="h-5 w-5 text-yellow-600 mt-0.5" />
              <div>
                <h4 className="font-semibold text-yellow-800 dark:text-yellow-200 mb-2">
                  🔒 Sistema de Consistência de Análise Ativo
                </h4>
                <div className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <p>• <strong>Garantia de credibilidade:</strong> Não apontaremos erros novos em documentos não alterados</p>
                  <p>• <strong>Rastreamento:</strong> Todos os problemas são registrados na primeira detecção</p>
                  <p>• <strong>Transparência:</strong> Indicamos claramente quais documentos foram alterados</p>
                  <p>• <strong>Proteção CLMP:</strong> Evita questionamentos sobre "erros que apareceram depois"</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Upload de Documentos Revisados - Só aparece se há histórico */}
      {modoReanalise && historicoProcesso && (
        <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <RefreshCw className="mr-2 h-5 w-5" />
              Upload de Documentos Revisados
              <Badge variant="outline" className="ml-2">
                Retorno #{historicoProcesso.retornos.length + 1}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <label className="text-sm font-medium mb-2 block">
                  Observações sobre os ajustes realizados:
                </label>
                <Textarea
                  placeholder="Descreva as correções e ajustes realizados nos documentos..."
                  value={observacoesAjuste}
                  onChange={(e) => setObservacoesAjuste(e.target.value)}
                  className="min-h-[80px]"
                />
              </div>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {(['etp', 'edital', 'tr'] as const).map((tipo) => (
                  <Card key={`revisado-${tipo}`} className="border-blue-300">
                    <CardHeader>
                      <CardTitle className="flex items-center text-lg">
                        <Upload className="mr-2 h-5 w-5" />
                        {tipo.toUpperCase()} Revisado
                        {historicoProcesso.versoes[tipo].length > 0 && (
                          <Badge variant="secondary" className="ml-2 text-xs">
                            v{historicoProcesso.versoes[tipo].length + 1}
                          </Badge>
                        )}
                      </CardTitle>
                    </CardHeader>
                    <CardContent className="space-y-4">
                      <div className="space-y-2">
                        <input
                          type="file"
                          id={`upload-revisado-${tipo}`}
                          accept=".pdf,.doc,.docx,.odt"
                          className="hidden"
                          onChange={(e) => {
                            const file = e.target.files?.[0];
                            if (file) handleFileUpload(tipo, file, true);
                          }}
                        />
                        <Button
                          variant="outline"
                          className="w-full border-blue-300 text-blue-700 hover:bg-blue-50"
                          disabled={loading}
                          onClick={() => document.getElementById(`upload-revisado-${tipo}`)?.click()}
                        >
                          <Upload className="mr-2 h-4 w-4" />
                          Upload Revisado
                        </Button>

                        {/* Recomendação de formato para revisados */}
                        <div className="text-center space-y-1">
                          <p className="text-xs text-green-600 dark:text-green-400 font-medium">
                            📄 PDF RECOMENDADO (melhor performance)
                          </p>
                          <p className="text-xs text-muted-foreground">
                            Aceitos: PDF, DOC, DOCX, ODT
                          </p>
                        </div>

                        {documentosRevisados[tipo] && (
                          <div className="text-xs text-blue-600 dark:text-blue-400 p-2 bg-blue-50 dark:bg-blue-950/30 rounded border">
                            ✅ {documentosRevisados[tipo]?.name}
                          </div>
                        )}

                        {/* Mostrar versões anteriores */}
                        {historicoProcesso.versoes[tipo].length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            <p className="font-medium mb-1">Versões anteriores:</p>
                            {historicoProcesso.versoes[tipo].map((versao, index) => (
                              <div key={index} className="flex justify-between items-center p-1">
                                <span>v{versao.versao} - {versao.dataUpload.toLocaleDateString('pt-BR')}</span>
                                {versao.scoreAnterior && (
                                  <span className="text-red-600">Score: {versao.scoreAnterior}%</span>
                                )}
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Botão de Análise */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
        <CardContent className="p-6 text-center space-y-4">
          <div className="flex items-center justify-center space-x-2 text-blue-700 dark:text-blue-300">
            <Brain className="h-5 w-5" />
            <Scale className="h-5 w-5" />
            <Zap className="h-5 w-5" />
          </div>
          <h3 className="text-lg font-semibold text-blue-800 dark:text-blue-200">
            {modoReanalise ? 'Reanalise Inteligente' : 'Análise Inteligente Completa'}
          </h3>
          <p className="text-sm text-blue-700 dark:text-blue-300 max-w-md mx-auto">
            {modoReanalise
              ? `Reanalise após ${historicoProcesso?.retornos.length || 0} retorno(s) - Machine Learning + Lei 14.133/21 + Decreto 9337/2024`
              : 'Machine Learning + Lei 14.133/21 + Decreto 9337/2024'
            }
          </p>
          <Button
            size="lg"
            className="w-full max-w-md"
            disabled={loading}
            onClick={handleAnalise}
          >
            {loading ? (
              <RefreshCw className="mr-2 h-5 w-5 animate-spin" />
            ) : (
              <Brain className="mr-2 h-5 w-5" />
            )}
            {loading
              ? (modoReanalise ? 'Reanalisando...' : 'Analisando...')
              : (modoReanalise ? 'Realizar Reanalise' : 'Realizar Análise Inteligente')
            }
          </Button>
        </CardContent>
      </Card>

      {/* Evolução de Scores - Só aparece em reanalise */}
      {modoReanalise && historicoProcesso && historicoProcesso.retornos.length > 0 && (
        <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50 mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-lg">
              <TrendingUp className="mr-2 h-5 w-5" />
              Evolução dos Scores por Retorno
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {historicoProcesso.retornos.map((retorno, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-white dark:bg-gray-900 rounded border">
                  <div>
                    <span className="font-medium">Retorno #{retorno.numero}</span>
                    <span className="text-sm text-muted-foreground ml-2">
                      {retorno.data.toLocaleDateString('pt-BR')}
                    </span>
                  </div>
                  <div className="flex items-center gap-4">
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Score</div>
                      <div className={`font-bold ${
                        retorno.scoreAnterior >= 80 ? 'text-green-600' :
                        retorno.scoreAnterior >= 60 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {retorno.scoreAnterior}%
                        {retorno.scoreAtual && (
                          <span className="text-green-600 ml-2">→ {retorno.scoreAtual}%</span>
                        )}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm text-muted-foreground">Problemas</div>
                      <div className="font-bold text-red-600">{retorno.problemas.length}</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Dashboard de Resultados */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4 mb-6">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">85%</div>
            <div className="text-sm text-muted-foreground">Score Geral</div>
            <Progress value={85} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">90%</div>
            <div className="text-sm text-muted-foreground">Lei 14.133/21</div>
            <Progress value={90} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">88%</div>
            <div className="text-sm text-muted-foreground">Decreto 9337/24</div>
            <Progress value={88} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">80%</div>
            <div className="text-sm text-muted-foreground">Análise IA</div>
            <Progress value={80} className="mt-2" />
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">95%</div>
            <div className="text-sm text-muted-foreground">Confiança IA</div>
            <Progress value={95} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Status Geral */}
      <Card className="mb-6 border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50">
        <CardContent className="p-6 text-center">
          <div className="space-y-4">
            <CheckCircle className="h-16 w-16 text-green-600 mx-auto" />
            <h3 className="text-lg font-semibold text-green-800 dark:text-green-200">
              ANÁLISE APROVADA
            </h3>
            <p className="text-sm text-green-700 dark:text-green-300">
              Todas as verificações foram atendidas satisfatoriamente
            </p>

            {/* Botão Liberar Pesquisa de Preços */}
            <div className="pt-4 border-t border-green-200">
              <Button
                size="lg"
                className="bg-blue-600 hover:bg-blue-700 text-white"
                onClick={() => setMostrarDistribuicao(true)}
              >
                <Search className="mr-2 h-5 w-5" />
                Liberar Pesquisa de Preços
              </Button>
              <p className="text-xs text-green-600 mt-2">
                Edital aprovado - Pronto para pesquisa de preços
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Checklist Visual Item por Item */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <FileCheck className="mr-2 h-5 w-5" />
            Checklist Visual - Lei 14.133/21 + Detecção de Conflitos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">

            {/* ETP - Estudo Técnico Preliminar */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg border-b pb-2 flex items-center">
                <FileText className="mr-2 h-4 w-4" />
                ETP - Estudo Técnico Preliminar
              </h4>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 6º, XX - Fundamentação do interesse público</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 5º - Observância dos princípios da licitação</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 18, I - Justificativa da necessidade</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 18, II - Descrição detalhada do objeto</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 18, III - Pesquisa de soluções de mercado</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 18, V - Análise de riscos</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Mapa de riscos conforme metodologia Mauá</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </div>

            {/* EDITAL */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg border-b pb-2 flex items-center">
                <Scale className="mr-2 h-4 w-4" />
                EDITAL - Análise de Conformidade
              </h4>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 40, I - Preâmbulo adequado</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 40, II - Objeto claramente definido</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 40, VII - Critérios de julgamento</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 40, VIII - Habilitação técnica/jurídica/fiscal</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Art. 28 - Modalidade conforme Lei 14.133/21</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Estrutura padrão Prefeitura Mauá</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </div>

            {/* TR - Termo de Referência */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg border-b pb-2 flex items-center">
                <FileCheck className="mr-2 h-4 w-4" />
                TR - Termo de Referência
                <Badge variant="outline" className="ml-2 text-xs">
                  Extraído do Edital (Anexo I)
                </Badge>
              </h4>

              {/* Alerta de TR Extraído */}
              <div className="p-3 bg-blue-50 dark:bg-blue-950/50 rounded border border-blue-200 dark:border-blue-800">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-blue-800 dark:text-blue-200">
                      TR identificado automaticamente no Edital
                    </p>
                    <p className="text-xs text-blue-700 dark:text-blue-300 mt-1">
                      Encontrado como "Anexo I" com 85% de confiança. Upload de TR separado não necessário.
                    </p>
                  </div>
                </div>
              </div>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Conformidade com ETP aprovado</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Especificações técnicas detalhadas</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Quantitativos e unidades de medida</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Critérios de aceitação e avaliação</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Obrigações do contratado/contratante</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Fiscalização e gestão contratual</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </div>

            {/* DECRETO MUNICIPAL 9337/2024 */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg border-b pb-2 flex items-center text-green-600">
                <FileCheck className="mr-2 h-4 w-4" />
                DECRETO MUNICIPAL 9337/2024
              </h4>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Identificação da Prefeitura de Mauá</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Competência da CLMP</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Padrão municipal (CNPJ, endereço, contatos)</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Identificação da secretaria solicitante</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Responsável técnico identificado</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Coerência entre documentos municipais</span>
                  <CheckCircle className="h-5 w-5 text-green-600" />
                </div>
              </div>
            </div>

            {/* COERÊNCIA DE OBJETOS - NOVO */}
            <div className="space-y-3">
              <h4 className="font-semibold text-lg border-b pb-2 flex items-center text-red-600">
                <AlertTriangle className="mr-2 h-4 w-4" />
                VERIFICAÇÃO DE COERÊNCIA DE OBJETOS
              </h4>

              <div className="grid grid-cols-1 gap-2">
                <div className="flex items-center justify-between p-2 bg-red-50 dark:bg-red-950/30 rounded border border-red-200">
                  <span className="text-sm font-medium">Objetos coerentes entre ETP/Edital/TR</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="p-3 bg-red-100 dark:bg-red-950/50 rounded border border-red-300">
                  <p className="text-sm text-red-800 dark:text-red-200 font-medium">
                    ⚠️ CONFLITO DETECTADO: Medicamentos vs Ração Animal
                  </p>
                  <p className="text-xs text-red-700 dark:text-red-300 mt-1">
                    ETP menciona "medicamentos" mas Edital refere-se a "ração animal" - objetos completamente diferentes!
                  </p>
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Similaridade entre documentos ≥ 50%</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
                <div className="flex items-center justify-between p-2 bg-muted/30 rounded">
                  <span className="text-sm">Palavras-chave consistentes</span>
                  <XCircle className="h-5 w-5 text-red-600" />
                </div>
              </div>
            </div>

          </div>
        </CardContent>
      </Card>

      {/* Novas Funcionalidades Implementadas */}
      <Card className="border-green-200 bg-green-50/50 dark:border-green-800 dark:bg-green-950/50">
        <CardHeader>
          <CardTitle className="flex items-center text-lg text-green-800 dark:text-green-200">
            <CheckCircle className="mr-2 h-5 w-5" />
            ✅ NOVAS FUNCIONALIDADES IMPLEMENTADAS
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3 text-green-700 dark:text-green-300">🚀 Upload Inteligente</h4>
              <div className="space-y-2 text-sm">
                <p>✅ <strong>Reconhecimento automático</strong> de ETP/Edital/TR</p>
                <p>✅ <strong>Upload em lote</strong> com nomes diferentes</p>
                <p>✅ <strong>Detecção por conteúdo</strong> quando nome não é claro</p>
                <p>✅ <strong>Classificação inteligente</strong> baseada em padrões</p>
              </div>
            </div>

            <div>
              <h4 className="font-semibold mb-3 text-green-700 dark:text-green-300">📄 Extração de TR</h4>
              <div className="space-y-2 text-sm">
                <p>✅ <strong>TR extraído do Edital</strong> automaticamente</p>
                <p>✅ <strong>Identificação Anexo I</strong> com alta precisão</p>
                <p>✅ <strong>Não bloqueia análise</strong> se TR não enviado</p>
                <p>✅ <strong>Mantém campo TR</strong> para processos de obras</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Demonstração de Funcionalidades */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="mr-2 h-5 w-5" />
            Sistema Completo Implementado
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <div>
              <h4 className="font-semibold mb-3">🧠 Análise Inteligente</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>✅ Upload real de documentos PDF/DOC</p>
                <p>✅ <strong>Upload em lote</strong> com reconhecimento automático</p>
                <p>✅ <strong>Extração de TR do Edital</strong> (Anexo I)</p>
                <p>✅ Machine Learning para detecção de riscos</p>
                <p>✅ Análise de conformidade Lei 14.133/21</p>
                <p>✅ Checklist visual ✓/✗ item por item</p>
                <p>✅ Detecção de conflitos de objetos</p>
                <p>✅ Sistema de treinamento ML</p>
                <p>✅ Geração de despachos automáticos</p>
              </div>
            </div>
            
            <div>
              <h4 className="font-semibold mb-3">⚖️ Conformidade Legal</h4>
              <div className="space-y-2 text-sm text-muted-foreground">
                <p>✅ Todos os princípios Art. 5º</p>
                <p>✅ Elementos obrigatórios ETP (Art. 18)</p>
                <p>✅ Estrutura Edital (Art. 40)</p>
                <p>✅ Habilitação técnica/jurídica/fiscal</p>
                <p>✅ Coerência de objetos (medicamento ≠ ração)</p>
                <p>✅ Gestão e fiscalização contratual</p>
                <p>✅ Despacho para SAJ quando aprovado</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Modal de Distribuição de Pesquisa */}
      {mostrarDistribuicao && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
            <DistribuirPesquisa
              processoId={numeroProcesso || 'CC 045/2024'}
              processoObjeto="Aquisição de materiais de limpeza e higiene"
              onDistribuir={(pesquisadorId) => {
                alert(`✅ Pesquisa distribuída para: ${pesquisadorId}`);
                setMostrarDistribuicao(false);
              }}
              onCancelar={() => setMostrarDistribuicao(false)}
            />
          </div>
        </div>
      )}
    </div>
  );
}
