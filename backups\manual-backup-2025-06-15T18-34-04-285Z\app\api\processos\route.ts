import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    
    // Parâmetros de busca e filtros
    const search = searchParams.get('search') || undefined;
    const status = searchParams.get('status') || undefined;
    const modalidade = searchParams.get('modalidade') || undefined;
    const responsavel = searchParams.get('responsavel') || undefined;
    const requisitante = searchParams.get('requisitante') || undefined;

    console.log('🔍 API - Parâmetros recebidos:', {
      search, status, modalidade, responsavel, requisitante
    });
    
    // Parâmetros de paginação
    const page = parseInt(searchParams.get('page') || '1');
    const limit = parseInt(searchParams.get('limit') || '20');
    const offset = (page - 1) * limit;

    // Buscar processos com filtros
    const processos = await CSVReader.searchProcessos({
      search,
      status,
      modalidade,
      responsavel,
      requisitante,
    });

    console.log('🔍 API - Processos encontrados:', processos.length);

    // Aplicar paginação
    const totalProcessos = processos.length;
    const processosPaginados = processos.slice(offset, offset + limit);

    // Calcular informações de paginação
    const totalPages = Math.ceil(totalProcessos / limit);
    const hasNextPage = page < totalPages;
    const hasPrevPage = page > 1;

    return NextResponse.json({
      success: true,
      data: {
        processos: processosPaginados,
        pagination: {
          currentPage: page,
          totalPages,
          totalItems: totalProcessos,
          itemsPerPage: limit,
          hasNextPage,
          hasPrevPage,
        },
      },
    });
  } catch (error) {
    console.error('Erro na API de processos:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
      },
      { status: 500 }
    );
  }
}
