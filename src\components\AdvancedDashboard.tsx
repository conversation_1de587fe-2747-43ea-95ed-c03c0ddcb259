'use client';

import { useState, useEffect } from 'react';
import { 
  Chart as ChartJS, 
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
} from 'chart.js';
import { Bar, Pie, Line, Doughnut } from 'react-chartjs-2';
import <PERSON><PERSON> from 'lottie-react';
import { 
  Card, 
  CardContent, 
  CardDescription, 
  CardFooter, 
  CardHeader, 
  CardTitle 
} from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { formatCurrency, formatNumber, getStatusColor } from '@/lib/utils';
import { 
  TrendingUp, 
  TrendingDown, 
  AlertCircle, 
  CheckCircle2, 
  Clock, 
  Ban,
  BarChart3,
  Pie<PERSON><PERSON>,
  LineChart,
  RefreshCcw
} from 'lucide-react';

// Registrar componentes do Chart.js
ChartJS.register(
  CategoryScale, 
  LinearScale, 
  BarElement, 
  Title, 
  Tooltip, 
  Legend,
  ArcElement,
  PointElement,
  LineElement,
  Filler
);

// Animação de carregamento
import loadingAnimation from '@/assets/loading.json';

interface DashboardProps {
  statusDistribuicao: {[key: string]: number};
  modalidadeDistribuicao: {[key: string]: number};
  responsavelDistribuicao: {[key: string]: number};
  progressoTemporal: {[key: string]: number[]};
  valoresTotais: {[key: string]: number};
  loading?: boolean;
  onRefresh?: () => void;
}

export default function AdvancedDashboard({
  statusDistribuicao,
  modalidadeDistribuicao,
  responsavelDistribuicao,
  progressoTemporal,
  valoresTotais,
  loading = false,
  onRefresh
}: DashboardProps) {
  const [chartType, setChartType] = useState<'bar' | 'pie' | 'doughnut'>('doughnut');
  
  // Cores para gráficos com transparência
  const backgroundColors = [
    'rgba(54, 162, 235, 0.7)',
    'rgba(75, 192, 192, 0.7