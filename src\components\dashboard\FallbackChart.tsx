'use client';

interface FallbackChartProps {
  data: Array<{ name: string; value: number }>;
  type: 'bar' | 'pie';
  title: string;
  height?: number;
}

// 🔒 COMPONENTE FALLBACK ROBUSTO - NUNCA FALHA
export function FallbackChart({ data, type, title, height = 300 }: FallbackChartProps) {
  if (!data || data.length === 0) {
    return (
      <div className="flex items-center justify-center" style={{ height }}>
        <div className="text-muted-foreground">📊 Sem dados para exibir</div>
      </div>
    );
  }

  const maxValue = Math.max(...data.map(d => d.value));

  if (type === 'bar') {
    return (
      <div className="p-4" style={{ height }}>
        <h4 className="text-sm font-medium mb-4">{title}</h4>
        <div className="space-y-3 overflow-y-auto max-h-64">
          {data.slice(0, 8).map((item, index) => {
            const percentage = (item.value / maxValue) * 100;
            return (
              <div key={index} className="space-y-1">
                <div className="flex justify-between text-xs">
                  <span className="truncate max-w-32" title={item.name}>
                    {item.name.length > 20 ? item.name.substring(0, 20) + '...' : item.name}
                  </span>
                  <span className="font-medium">{item.value}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div 
                    className="bg-blue-500 h-2 rounded-full transition-all duration-300"
                    style={{ width: `${percentage}%` }}
                  />
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  if (type === 'pie') {
    const total = data.reduce((sum, item) => sum + item.value, 0);
    
    return (
      <div className="p-4" style={{ height }}>
        <h4 className="text-sm font-medium mb-4">{title}</h4>
        <div className="space-y-2 overflow-y-auto max-h-64">
          {data.slice(0, 6).map((item, index) => {
            const percentage = ((item.value / total) * 100).toFixed(1);
            const colors = ['#0088FE', '#00C49F', '#FFBB28', '#FF8042', '#8884D8', '#82CA9D'];
            const color = colors[index % colors.length];
            
            return (
              <div key={index} className="flex items-center space-x-3">
                <div 
                  className="w-4 h-4 rounded-full flex-shrink-0"
                  style={{ backgroundColor: color }}
                />
                <div className="flex-1 min-w-0">
                  <div className="flex justify-between items-center">
                    <span className="text-xs truncate" title={item.name}>
                      {item.name.length > 25 ? item.name.substring(0, 25) + '...' : item.name}
                    </span>
                    <span className="text-xs font-medium ml-2">
                      {item.value} ({percentage}%)
                    </span>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      </div>
    );
  }

  return (
    <div className="flex items-center justify-center" style={{ height }}>
      <div className="text-muted-foreground">⚠️ Tipo de gráfico não suportado</div>
    </div>
  );
}
