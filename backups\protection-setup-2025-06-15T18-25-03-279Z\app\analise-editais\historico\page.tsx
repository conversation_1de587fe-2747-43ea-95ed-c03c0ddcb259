'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import { 
  History, 
  Search, 
  Filter,
  Download,
  Eye,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  FileText,
  Brain,
  Scale,
  Clock,
  User,
  Building
} from 'lucide-react';

interface AnaliseHistorico {
  id: string;
  processo: string;
  secretaria: string;
  responsavel: string;
  dataAnalise: Date;
  status: 'aprovado' | 'reprovado' | 'pendente';
  scoreGeral: number;
  scoreLei: number;
  scoreDecreto: number;
  scoreIA: number;
  documentos: {
    etp: boolean;
    edital: boolean;
    tr: boolean;
  };
  problemas: number;
  observacoes: string;
}

export default function HistoricoPage() {
  const [filtro, setFiltro] = useState('');
  const [statusFiltro, setStatusFiltro] = useState<string>('todos');

  // Mock data - em produção viria do backend
  const historico: AnaliseHistorico[] = [
    {
      id: '1',
      processo: 'PE 001/2025 - Medicamentos Básicos',
      secretaria: 'Secretaria de Saúde',
      responsavel: 'Dr. João Silva',
      dataAnalise: new Date('2025-01-06'),
      status: 'aprovado',
      scoreGeral: 92,
      scoreLei: 95,
      scoreDecreto: 90,
      scoreIA: 88,
      documentos: { etp: true, edital: true, tr: true },
      problemas: 2,
      observacoes: 'Pequenos ajustes em especificações técnicas'
    },
    {
      id: '2',
      processo: 'CC 045/2024 - Ração Animal',
      secretaria: 'Secretaria de Meio Ambiente',
      responsavel: 'Maria Santos',
      dataAnalise: new Date('2025-01-05'),
      status: 'reprovado',
      scoreGeral: 65,
      scoreLei: 70,
      scoreDecreto: 85,
      scoreIA: 40,
      documentos: { etp: true, edital: true, tr: false },
      problemas: 8,
      observacoes: 'Conflito de objetos detectado - medicamentos vs ração'
    },
    {
      id: '3',
      processo: 'TP 012/2025 - Serviços de Limpeza',
      secretaria: 'Secretaria de Obras',
      responsavel: 'Carlos Oliveira',
      dataAnalise: new Date('2025-01-04'),
      status: 'aprovado',
      scoreGeral: 88,
      scoreLei: 90,
      scoreDecreto: 92,
      scoreIA: 82,
      documentos: { etp: true, edital: true, tr: true },
      problemas: 3,
      observacoes: 'TR extraído automaticamente do edital'
    },
    {
      id: '4',
      processo: 'PE 002/2025 - Material de Escritório',
      secretaria: 'Secretaria de Administração',
      responsavel: 'Ana Costa',
      dataAnalise: new Date('2025-01-03'),
      status: 'pendente',
      scoreGeral: 75,
      scoreLei: 80,
      scoreDecreto: 75,
      scoreIA: 70,
      documentos: { etp: true, edital: false, tr: false },
      problemas: 5,
      observacoes: 'Aguardando upload do edital completo'
    },
    {
      id: '5',
      processo: 'CC 046/2024 - Equipamentos Médicos',
      secretaria: 'Secretaria de Saúde',
      responsavel: 'Dr. Pedro Lima',
      dataAnalise: new Date('2025-01-02'),
      status: 'aprovado',
      scoreGeral: 94,
      scoreLei: 96,
      scoreDecreto: 93,
      scoreIA: 92,
      documentos: { etp: true, edital: true, tr: true },
      problemas: 1,
      observacoes: 'Análise exemplar - padrão de qualidade'
    }
  ];

  const historicoFiltrado = historico.filter(item => {
    const matchFiltro = item.processo.toLowerCase().includes(filtro.toLowerCase()) ||
                       item.secretaria.toLowerCase().includes(filtro.toLowerCase()) ||
                       item.responsavel.toLowerCase().includes(filtro.toLowerCase());
    
    const matchStatus = statusFiltro === 'todos' || item.status === statusFiltro;
    
    return matchFiltro && matchStatus;
  });

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'aprovado':
        return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'reprovado':
        return <XCircle className="h-4 w-4 text-red-600" />;
      case 'pendente':
        return <AlertTriangle className="h-4 w-4 text-yellow-600" />;
      default:
        return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'aprovado':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'reprovado':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'pendente':
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'text-green-600';
    if (score >= 80) return 'text-blue-600';
    if (score >= 70) return 'text-yellow-600';
    return 'text-red-600';
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Histórico de Análises</h1>
          <p className="text-muted-foreground mt-2">
            Registro completo de todas as análises realizadas pelo sistema
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <History className="mr-1 h-3 w-3" />
            {historicoFiltrado.length} Análises
          </Badge>
          <Button variant="outline" size="sm">
            <Download className="mr-2 h-4 w-4" />
            Exportar
          </Button>
        </div>
      </div>

      {/* Filtros */}
      <Card>
        <CardContent className="p-4">
          <div className="flex flex-col sm:flex-row gap-4">
            <div className="flex-1">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Buscar por processo, secretaria ou responsável..."
                  value={filtro}
                  onChange={(e) => setFiltro(e.target.value)}
                  className="pl-10"
                />
              </div>
            </div>
            <div className="flex gap-2">
              <Button
                variant={statusFiltro === 'todos' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFiltro('todos')}
              >
                Todos
              </Button>
              <Button
                variant={statusFiltro === 'aprovado' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFiltro('aprovado')}
              >
                Aprovados
              </Button>
              <Button
                variant={statusFiltro === 'reprovado' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFiltro('reprovado')}
              >
                Reprovados
              </Button>
              <Button
                variant={statusFiltro === 'pendente' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setStatusFiltro('pendente')}
              >
                Pendentes
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Lista de Análises */}
      <div className="space-y-4">
        {historicoFiltrado.map((analise) => (
          <Card key={analise.id} className="hover:shadow-md transition-shadow">
            <CardContent className="p-6">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
                {/* Informações Principais */}
                <div className="flex-1">
                  <div className="flex items-center gap-3 mb-2">
                    <h3 className="font-semibold text-lg">{analise.processo}</h3>
                    <Badge className={`${getStatusColor(analise.status)} border-0`}>
                      {getStatusIcon(analise.status)}
                      <span className="ml-1 capitalize">{analise.status}</span>
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-muted-foreground">
                    <div className="flex items-center">
                      <Building className="h-4 w-4 mr-2" />
                      {analise.secretaria}
                    </div>
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      {analise.responsavel}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-2" />
                      {analise.dataAnalise.toLocaleDateString('pt-BR')}
                    </div>
                    <div className="flex items-center">
                      <AlertTriangle className="h-4 w-4 mr-2" />
                      {analise.problemas} problema(s) detectado(s)
                    </div>
                  </div>

                  {analise.observacoes && (
                    <p className="text-sm text-muted-foreground mt-2 italic">
                      "{analise.observacoes}"
                    </p>
                  )}
                </div>

                {/* Scores */}
                <div className="lg:w-80">
                  <div className="grid grid-cols-2 lg:grid-cols-4 gap-3">
                    <div className="text-center">
                      <div className={`text-2xl font-bold ${getScoreColor(analise.scoreGeral)}`}>
                        {analise.scoreGeral}%
                      </div>
                      <div className="text-xs text-muted-foreground">Geral</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-semibold ${getScoreColor(analise.scoreLei)}`}>
                        {analise.scoreLei}%
                      </div>
                      <div className="text-xs text-muted-foreground">Lei 14.133</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-semibold ${getScoreColor(analise.scoreDecreto)}`}>
                        {analise.scoreDecreto}%
                      </div>
                      <div className="text-xs text-muted-foreground">Decreto</div>
                    </div>
                    <div className="text-center">
                      <div className={`text-lg font-semibold ${getScoreColor(analise.scoreIA)}`}>
                        {analise.scoreIA}%
                      </div>
                      <div className="text-xs text-muted-foreground">IA</div>
                    </div>
                  </div>

                  {/* Documentos */}
                  <div className="flex justify-center gap-2 mt-3">
                    <Badge variant={analise.documentos.etp ? 'default' : 'secondary'} className="text-xs">
                      ETP {analise.documentos.etp ? '✓' : '✗'}
                    </Badge>
                    <Badge variant={analise.documentos.edital ? 'default' : 'secondary'} className="text-xs">
                      Edital {analise.documentos.edital ? '✓' : '✗'}
                    </Badge>
                    <Badge variant={analise.documentos.tr ? 'default' : 'secondary'} className="text-xs">
                      TR {analise.documentos.tr ? '✓' : '✗'}
                    </Badge>
                  </div>

                  {/* Ações */}
                  <div className="flex justify-center gap-2 mt-3">
                    <Button variant="outline" size="sm">
                      <Eye className="h-4 w-4 mr-1" />
                      Ver Detalhes
                    </Button>
                    <Button variant="outline" size="sm">
                      <Download className="h-4 w-4 mr-1" />
                      Relatório
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {historicoFiltrado.length === 0 && (
        <Card>
          <CardContent className="p-12 text-center">
            <History className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <h3 className="text-lg font-semibold mb-2">Nenhuma análise encontrada</h3>
            <p className="text-muted-foreground">
              {filtro ? 'Tente ajustar os filtros de busca' : 'Ainda não há análises realizadas'}
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
