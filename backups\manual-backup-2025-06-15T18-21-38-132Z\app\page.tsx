
import Link from 'next/link';
import { ArrowRight } from 'lucide-react';

export default function HomePage() {
  return (
    <div className="min-h-screen flex flex-col bg-background">
      <header className="bg-card shadow-sm border-b border-border">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 flex justify-between items-center py-6">
          <div className="flex items-center">
            <div className="bg-primary text-primary-foreground font-bold text-xl px-3 py-1 rounded mr-3">IP</div>
            <h1 className="text-2xl font-bold text-foreground">InovaProcess</h1>
          </div>
          <Link
            href="/login"
            className="bg-primary hover:bg-primary/90 text-primary-foreground font-medium py-2 px-4 rounded-lg transition-colors duration-200 flex items-center"
          >
            Acessar Sistema
            <ArrowRight size={16} className="ml-2" />
          </Link>
        </div>
      </header>

      <main className="flex-grow">
        <div className="max-w-7xl mx-auto py-12 px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl md:text-6xl font-extrabold text-foreground mb-6">
              Plataforma Inteligente de Gestão Institucional
            </h2>
            <p className="max-w-4xl mx-auto text-xl text-muted-foreground mb-8">
              Solução tecnológica avançada que moderniza a gestão de processos organizacionais através de inteligência artificial, automação inteligente e análise de dados em tempo real. Preparado para integração com APIs, blockchain e smart contracts.
            </p>
            <div className="flex flex-wrap justify-center gap-4 text-sm">
              <span className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 py-1 rounded-full font-medium">🤖 Inteligência Artificial</span>
              <span className="bg-green-100 dark:bg-green-900/30 text-green-700 dark:text-green-300 px-3 py-1 rounded-full font-medium">⚡ Tempo Real</span>
              <span className="bg-purple-100 dark:bg-purple-900/30 text-purple-700 dark:text-purple-300 px-3 py-1 rounded-full font-medium">📊 Business Intelligence</span>
              <span className="bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-3 py-1 rounded-full font-medium">🔗 Blockchain Ready</span>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-16">
            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 border border-border group flex flex-col h-full">
              <div className="h-10 w-10 bg-blue-100 dark:bg-blue-900/30 rounded-lg flex items-center justify-center text-blue-600 dark:text-blue-400 mb-3 group-hover:scale-110 transition-transform">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Gestão de Processos</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Controle completo de licitações com automação e monitoramento em tempo real.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-1 transition-transform mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 border border-border group flex flex-col h-full">
              <div className="h-10 w-10 bg-green-100 dark:bg-green-900/30 rounded-lg flex items-center justify-center text-green-600 dark:text-green-400 mb-3 group-hover:scale-110 transition-transform">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Análise de Editais</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Ferramenta avançada para validação automática de documentos e checklist de conformidade.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-1 transition-transform mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 border border-border group flex flex-col h-full">
              <div className="h-10 w-10 bg-purple-100 dark:bg-purple-900/30 rounded-lg flex items-center justify-center text-purple-600 dark:text-purple-400 mb-3 group-hover:scale-110 transition-transform">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Pesquisa de Preços</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Integração com PNCP para pesquisas automatizadas e comparações inteligentes de valores.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-1 transition-transform mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1" />
              </Link>
            </div>

            <div className="bg-card p-5 rounded-lg shadow-sm hover:shadow-lg transition-all duration-300 border border-border group flex flex-col h-full">
              <div className="h-10 w-10 bg-orange-100 dark:bg-orange-900/30 rounded-lg flex items-center justify-center text-orange-600 dark:text-orange-400 mb-3 group-hover:scale-110 transition-transform">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" className="h-5 w-5">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                </svg>
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">Gestão de Contratos</h3>
              <p className="text-muted-foreground text-sm mb-4 flex-grow">
                Controle completo de contratos de obras e serviços com alertas automáticos.
              </p>
              <Link href="/login" className="text-primary font-medium flex items-center hover:text-primary/80 text-sm group-hover:translate-x-1 transition-transform mt-auto">
                Acessar Módulo <ArrowRight size={14} className="ml-1" />
              </Link>
            </div>
          </div>

          {/* Seção de Apresentação de Alto Nível */}
          <div className="bg-gradient-to-r from-blue-500 to-purple-500 dark:from-blue-700 dark:to-purple-700 rounded-2xl p-8 md:p-12 text-white mb-16">
            <div className="max-w-4xl mx-auto text-center">
              <h2 className="text-3xl md:text-4xl font-bold mb-6">
                Tecnologia de Ponta em Gestão de Processos
              </h2>
              <p className="text-lg md:text-xl mb-8 opacity-90">
                Plataforma desenvolvida com as mais avançadas tecnologias de mercado, incluindo
                inteligência artificial, automação inteligente e preparação para blockchain.
                Ideal para organizações que buscam excelência operacional e inovação.
              </p>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-2xl font-bold mb-1">+80%</div>
                  <div className="text-sm opacity-80">Eficiência Operacional</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-2xl font-bold mb-1">100%</div>
                  <div className="text-sm opacity-80">Transparência e Auditoria</div>
                </div>
                <div className="bg-white/10 backdrop-blur-sm rounded-lg p-4">
                  <div className="text-2xl font-bold mb-1">24/7</div>
                  <div className="text-sm opacity-80">Monitoramento Inteligente</div>
                </div>
              </div>

              <div className="flex flex-wrap justify-center gap-4">
                <Link
                  href="/login"
                  className="bg-white text-blue-600 hover:bg-gray-100 font-semibold py-3 px-6 rounded-lg transition-colors duration-200 flex items-center"
                >
                  Acessar Sistema
                  <ArrowRight size={18} className="ml-2" />
                </Link>
                <Link
                  href="/login"
                  className="border-2 border-white text-white hover:bg-white hover:text-blue-600 font-semibold py-3 px-6 rounded-lg transition-colors duration-200"
                >
                  Fazer Login
                </Link>
              </div>
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}

