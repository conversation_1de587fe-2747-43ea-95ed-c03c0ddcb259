# CONFIGURAÇÕES DE AMBIENTE SEGURO - InovaProcess

## 🔧 VA<PERSON>ÁVEIS DE AMBIENTE

### Arquivo .env.production

```bash
# ===== CONFIGURAÇÕES BÁSICAS =====
NODE_ENV=production
PORT=3000
NEXT_PUBLIC_APP_URL=https://inovaprocess.municipio.gov.br

# ===== BANCO DE DADOS =====
DATABASE_URL=postgresql://inovaprocess_user:SENHA_SUPER_SEGURA@localhost:5432/inovaprocess_prod?sslmode=require
DATABASE_SSL=true
DATABASE_POOL_MIN=2
DATABASE_POOL_MAX=10

# ===== AUTENTICAÇÃO =====
JWT_SECRET=sua_chave_jwt_super_segura_com_256_bits_de_entropia_aqui
JWT_EXPIRES_IN=8h
REFRESH_TOKEN_SECRET=outra_chave_super_segura_para_refresh_tokens
SESSION_SECRET=chave_para_sessoes_web_muito_segura

# ===== GOOGLE OAUTH =====
GOOGLE_CLIENT_ID=123456789-abcdefghijklmnop.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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aqui
GOOGLE_REDIRECT_URI=https://inovaprocess.municipio.gov.br/api/auth/callback/google

# ===== CRIPTOGRAFIA =====
ENCRYPTION_KEY=chave_aes_256_para_criptografia_de_dados_sensiveis
HASH_SALT_ROUNDS=12

# ===== LOGS E MONITORAMENTO =====
LOG_LEVEL=info
LOG_FILE_PATH=/var/log/inovaprocess/app.log
SENTRY_DSN=https://<EMAIL>/projeto

# ===== EMAIL (OPCIONAL) =====
SMTP_HOST=smtp.municipio.gov.br
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=senha_do_email_institucional
SMTP_FROM=InovaProcess <<EMAIL>>

# ===== SEGURANÇA =====
CORS_ORIGIN=https://inovaprocess.municipio.gov.br
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
CSRF_SECRET=chave_para_protecao_csrf

# ===== BACKUP =====
BACKUP_ENCRYPTION_KEY=chave_para_criptografar_backups
BACKUP_STORAGE_PATH=/backup/inovaprocess
BACKUP_RETENTION_DAYS=90

# ===== INTEGRAÇÃO PNCP =====
PNCP_API_URL=https://pncp.gov.br/api
PNCP_API_KEY=sua_chave_api_pncp
PNCP_TIMEOUT=30000

# ===== CACHE =====
REDIS_URL=redis://localhost:6379/0
CACHE_TTL=3600
```

## 🔐 CONFIGURAÇÃO DO NGINX

### /etc/nginx/sites-available/inovaprocess

```nginx
# Redirecionamento HTTP para HTTPS
server {
    listen 80;
    server_name inovaprocess.municipio.gov.br;
    return 301 https://$server_name$request_uri;
}

# Configuração HTTPS
server {
    listen 443 ssl http2;
    server_name inovaprocess.municipio.gov.br;

    # Certificados SSL
    ssl_certificate /etc/ssl/certs/inovaprocess.crt;
    ssl_certificate_key /etc/ssl/private/inovaprocess.key;
    
    # Configurações SSL seguras
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Headers de segurança
    add_header Strict-Transport-Security "max-age=********; includeSubDomains" always;
    add_header X-Content-Type-Options nosniff always;
    add_header X-Frame-Options DENY always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' https://accounts.google.com; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self'; connect-src 'self' https://accounts.google.com; frame-src https://accounts.google.com;" always;

    # Configurações de proxy
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Arquivos estáticos
    location /_next/static/ {
        proxy_pass http://localhost:3000;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }

    # Rate limiting
    limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;
    location /api/auth/ {
        limit_req zone=login burst=5 nodelay;
        proxy_pass http://localhost:3000;
    }

    # Logs
    access_log /var/log/nginx/inovaprocess_access.log;
    error_log /var/log/nginx/inovaprocess_error.log;
}
```

## 🛡️ CONFIGURAÇÃO DO FIREWALL (UFW)

```bash
# Resetar firewall
sudo ufw --force reset

# Políticas padrão
sudo ufw default deny incoming
sudo ufw default allow outgoing

# SSH (apenas para IPs específicos)
sudo ufw allow from ***********/24 to any port 22

# HTTP/HTTPS
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# PostgreSQL (apenas localhost)
sudo ufw allow from 127.0.0.1 to any port 5432

# Redis (apenas localhost)
sudo ufw allow from 127.0.0.1 to any port 6379

# Ativar firewall
sudo ufw enable

# Verificar status
sudo ufw status verbose
```

## 🗄️ CONFIGURAÇÃO DO POSTGRESQL

### postgresql.conf

```ini
# Conexões
listen_addresses = 'localhost'
port = 5432
max_connections = 100

# SSL
ssl = on
ssl_cert_file = '/etc/ssl/certs/server.crt'
ssl_key_file = '/etc/ssl/private/server.key'

# Logs
log_destination = 'stderr'
logging_collector = on
log_directory = '/var/log/postgresql'
log_filename = 'postgresql-%Y-%m-%d_%H%M%S.log'
log_statement = 'mod'
log_min_duration_statement = 1000

# Segurança
password_encryption = scram-sha-256
```

### pg_hba.conf

```ini
# TYPE  DATABASE        USER            ADDRESS                 METHOD
local   all             postgres                                peer
local   all             all                                     scram-sha-256
host    inovaprocess_prod inovaprocess_user 127.0.0.1/32       scram-sha-256
host    all             all             127.0.0.1/32            reject
```

## 🔄 SCRIPTS DE DEPLOY

### deploy.sh

```bash
#!/bin/bash

# Configurações
APP_DIR="/var/www/inovaprocess"
BACKUP_DIR="/backup/inovaprocess"
LOG_FILE="/var/log/inovaprocess/deploy.log"

# Função de log
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1" | tee -a $LOG_FILE
}

# Backup antes do deploy
log "Iniciando backup pré-deploy..."
pg_dump inovaprocess_prod > $BACKUP_DIR/pre-deploy-$(date +%Y%m%d-%H%M%S).sql

# Parar aplicação
log "Parando aplicação..."
sudo systemctl stop inovaprocess

# Atualizar código
log "Atualizando código..."
cd $APP_DIR
git pull origin main

# Instalar dependências
log "Instalando dependências..."
npm ci --production

# Build da aplicação
log "Fazendo build..."
npm run build

# Executar migrações
log "Executando migrações..."
npm run migrate

# Iniciar aplicação
log "Iniciando aplicação..."
sudo systemctl start inovaprocess

# Verificar status
log "Verificando status..."
sleep 10
if curl -f http://localhost:3000/health; then
    log "Deploy realizado com sucesso!"
else
    log "ERRO: Aplicação não está respondendo!"
    exit 1
fi
```

## 📊 MONITORAMENTO

### Configuração do PM2

```javascript
// ecosystem.config.js
module.exports = {
  apps: [{
    name: 'inovaprocess',
    script: 'npm',
    args: 'start',
    cwd: '/var/www/inovaprocess',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/inovaprocess/error.log',
    out_file: '/var/log/inovaprocess/out.log',
    log_file: '/var/log/inovaprocess/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

## 🔒 HARDENING DO SISTEMA

### Configurações do sistema

```bash
# Atualizar sistema
sudo apt update && sudo apt upgrade -y

# Instalar fail2ban
sudo apt install fail2ban -y

# Configurar fail2ban
sudo cp /etc/fail2ban/jail.conf /etc/fail2ban/jail.local

# Desabilitar serviços desnecessários
sudo systemctl disable apache2
sudo systemctl disable sendmail

# Configurar logrotate
sudo nano /etc/logrotate.d/inovaprocess
```

### /etc/logrotate.d/inovaprocess

```
/var/log/inovaprocess/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 inovaprocess inovaprocess
    postrotate
        systemctl reload inovaprocess
    endscript
}
```

---

**⚠️ IMPORTANTE:** 
- Todas as senhas e chaves devem ser geradas de forma segura
- Nunca commitar arquivos .env no repositório
- Usar um gerenciador de secrets em produção
- Testar todas as configurações em ambiente de staging primeiro

**Documento criado em:** 15 de junho de 2025
