import { NextRequest, NextResponse } from 'next/server';
import { writeFile, readFile } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';

interface ClassificacaoRequest {
  documentoId: string;
  categoria: 'aprovado' | 'reprovado' | 'conflito';
  observacoes?: string;
  problemas?: string[];
  motivoClassificacao?: string;
}

export async function POST(request: NextRequest) {
  try {
    const { documentoId, categoria, observacoes, problemas, motivoClassificacao } = 
      await request.json() as ClassificacaoRequest;

    if (!documentoId || !categoria) {
      return NextResponse.json(
        { success: false, error: 'Dados insuficientes para classificação' },
        { status: 400 }
      );
    }

    // Buscar o documento no sistema de treinamento
    const documento = await buscarDocumento(documentoId);
    
    if (!documento) {
      return NextResponse.json(
        { success: false, error: 'Documento não encontrado' },
        { status: 404 }
      );
    }

    // Atualizar classificação
    const classificacaoAtualizada = await atualizarClassificacao(
      documento, 
      categoria, 
      observacoes, 
      problemas, 
      motivoClassificacao
    );

    // Mover arquivo para diretório correto se necessário
    await moverArquivoSeNecessario(documento, categoria);

    // Registrar no log de treinamento
    await registrarLogTreinamento(documentoId, categoria, motivoClassificacao);

    // Atualizar estatísticas do dataset
    await atualizarEstatisticasDataset();

    return NextResponse.json({
      success: true,
      data: {
        documentoId,
        categoriaAnterior: documento.categoria,
        categoriaNova: categoria,
        classificacao: classificacaoAtualizada,
        timestamp: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('Erro na classificação:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const categoria = searchParams.get('categoria');
    const tipo = searchParams.get('tipo');
    const secretaria = searchParams.get('secretaria');

    const documentos = await listarDocumentosTreinamento({
      categoria,
      tipo,
      secretaria
    });

    const estatisticas = await obterEstatisticasDataset();

    return NextResponse.json({
      success: true,
      data: {
        documentos,
        estatisticas,
        total: documentos.length
      }
    });

  } catch (error) {
    console.error('Erro ao listar documentos:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

async function buscarDocumento(documentoId: string): Promise<any | null> {
  // Simular busca no banco de dados/filesystem
  const treinamentoDir = path.join(process.cwd(), 'data', 'treinamento-ml');
  const categorias = ['aprovado', 'reprovado', 'conflito', 'nao_classificado'];
  
  for (const categoria of categorias) {
    const categoriaDir = path.join(treinamentoDir, categoria);
    const metadataPath = path.join(categoriaDir, `${documentoId}.metadata.json`);
    
    if (existsSync(metadataPath)) {
      try {
        const metadata = JSON.parse(await readFile(metadataPath, 'utf-8'));
        return { ...metadata, id: documentoId, categoria };
      } catch (error) {
        console.error(`Erro ao ler metadata de ${documentoId}:`, error);
      }
    }
  }
  
  return null;
}

async function atualizarClassificacao(
  documento: any, 
  categoria: string, 
  observacoes?: string, 
  problemas?: string[], 
  motivoClassificacao?: string
): Promise<any> {
  
  const classificacaoAtualizada = {
    ...documento,
    categoria,
    observacoes: observacoes || documento.observacoes,
    problemas: problemas || documento.problemas,
    motivoClassificacao,
    dataClassificacao: new Date().toISOString(),
    classificadoPor: 'CLMP', // Em produção, usar usuário logado
    versaoClassificacao: (documento.versaoClassificacao || 0) + 1
  };

  // Salvar metadata atualizada
  const treinamentoDir = path.join(process.cwd(), 'data', 'treinamento-ml');
  const categoriaDir = path.join(treinamentoDir, categoria);
  const metadataPath = path.join(categoriaDir, `${documento.id}.metadata.json`);
  
  await writeFile(metadataPath, JSON.stringify(classificacaoAtualizada, null, 2));
  
  return classificacaoAtualizada;
}

async function moverArquivoSeNecessario(documento: any, novaCategoria: string): Promise<void> {
  if (documento.categoria === novaCategoria) return;
  
  const treinamentoDir = path.join(process.cwd(), 'data', 'treinamento-ml');
  const categoriaAntigaDir = path.join(treinamentoDir, documento.categoria);
  const categoriaNova = path.join(treinamentoDir, novaCategoria);
  
  // Mover arquivo principal
  const arquivoAntigo = path.join(categoriaAntigaDir, documento.nomeArquivo);
  const arquivoNovo = path.join(categoriaNova, documento.nomeArquivo);
  
  // Mover metadata
  const metadataAntigo = path.join(categoriaAntigaDir, `${documento.id}.metadata.json`);
  const metadataNovo = path.join(categoriaNova, `${documento.id}.metadata.json`);
  
  // Em produção, implementar movimentação real dos arquivos
  console.log(`Movendo ${arquivoAntigo} para ${arquivoNovo}`);
  console.log(`Movendo ${metadataAntigo} para ${metadataNovo}`);
}

async function registrarLogTreinamento(
  documentoId: string, 
  categoria: string, 
  motivo?: string
): Promise<void> {
  const logEntry = {
    timestamp: new Date().toISOString(),
    documentoId,
    categoria,
    motivo,
    usuario: 'CLMP',
    acao: 'classificacao'
  };
  
  const logPath = path.join(process.cwd(), 'data', 'treinamento-ml', 'log-treinamento.json');
  
  let logs = [];
  if (existsSync(logPath)) {
    try {
      logs = JSON.parse(await readFile(logPath, 'utf-8'));
    } catch (error) {
      console.error('Erro ao ler log:', error);
    }
  }
  
  logs.push(logEntry);
  
  // Manter apenas os últimos 1000 logs
  if (logs.length > 1000) {
    logs = logs.slice(-1000);
  }
  
  await writeFile(logPath, JSON.stringify(logs, null, 2));
}

async function atualizarEstatisticasDataset(): Promise<void> {
  const estatisticas = await obterEstatisticasDataset();
  
  const statsPath = path.join(process.cwd(), 'data', 'treinamento-ml', 'estatisticas.json');
  
  const stats = {
    ...estatisticas,
    ultimaAtualizacao: new Date().toISOString(),
    qualidadeDataset: calcularQualidadeDataset(estatisticas)
  };
  
  await writeFile(statsPath, JSON.stringify(stats, null, 2));
}

async function obterEstatisticasDataset(): Promise<any> {
  // Simular estatísticas do dataset
  return {
    total: 15,
    aprovados: 8,
    reprovados: 4,
    conflitos: 2,
    naoClassificados: 1,
    porTipo: {
      etp: 5,
      edital: 6,
      tr: 4
    },
    porSecretaria: {
      'Saúde': 4,
      'Infraestrutura': 3,
      'Educação': 2,
      'Agricultura': 2,
      'Outras': 4
    }
  };
}

async function listarDocumentosTreinamento(filtros: any): Promise<any[]> {
  // Simular listagem de documentos
  const documentosSimulados = [
    {
      id: 'doc_001',
      nome: 'ETP_Medicamentos_Saude_2024.pdf',
      tipo: 'etp',
      categoria: 'aprovado',
      objeto: 'Aquisição de medicamentos básicos',
      secretaria: 'Saúde',
      dataUpload: '2024-01-15',
      tamanho: 2048,
      observacoes: 'Documento modelo - bem estruturado'
    },
    {
      id: 'doc_002',
      nome: 'Edital_Conflito_2024.pdf',
      tipo: 'edital',
      categoria: 'conflito',
      objeto: 'Compra de ração animal',
      secretaria: 'Agricultura',
      dataUpload: '2024-01-10',
      tamanho: 1536,
      problemas: ['Objeto conflitante com ETP', 'Habilitação incompleta']
    }
  ];
  
  let documentosFiltrados = documentosSimulados;
  
  if (filtros.categoria && filtros.categoria !== 'todos') {
    documentosFiltrados = documentosFiltrados.filter(doc => doc.categoria === filtros.categoria);
  }
  
  if (filtros.tipo) {
    documentosFiltrados = documentosFiltrados.filter(doc => doc.tipo === filtros.tipo);
  }
  
  if (filtros.secretaria) {
    documentosFiltrados = documentosFiltrados.filter(doc => doc.secretaria === filtros.secretaria);
  }
  
  return documentosFiltrados;
}

function calcularQualidadeDataset(stats: any): number {
  const totalClassificados = stats.aprovados + stats.reprovados + stats.conflitos;
  const percentualClassificado = totalClassificados / stats.total;
  
  // Qualidade baseada em:
  // - Percentual de documentos classificados
  // - Diversidade de categorias
  // - Quantidade mínima por categoria
  
  let qualidade = percentualClassificado * 0.4;
  
  // Bonificar diversidade
  if (stats.aprovados >= 5 && stats.reprovados >= 3 && stats.conflitos >= 2) {
    qualidade += 0.3;
  }
  
  // Bonificar quantidade total
  if (stats.total >= 15) {
    qualidade += 0.3;
  }
  
  return Math.min(100, Math.round(qualidade * 100));
}
