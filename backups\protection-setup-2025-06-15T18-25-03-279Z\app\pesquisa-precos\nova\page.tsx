'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Search,
  Download,
  ExternalLink,
  FileText,
  Calculator,
  TrendingUp,
  BarChart3,
  Filter,
  RefreshCw,
  AlertCircle,
  CheckCircle,
  Clock,
  Plus,
  Users,
  ArrowLeft
} from 'lucide-react';
import PesquisaPrecos from '@/components/PesquisaPrecos';
import DistribuirPesquisa from '@/components/DistribuirPesquisa';
import Link from 'next/link';

export default function NovaPesquisaPrecosPage() {
  const [modo, setModo] = useState<'lista' | 'pesquisa' | 'distribuir'>('lista');
  const [processoSelecionado, setProcessoSelecionado] = useState<string>('');

  // Simulação de processos aguardando pesquisa
  const processosAguardando = [
    {
      id: 'CC 045/2024',
      objeto: 'Aquisição de materiais de limpeza e higiene',
      secretaria: 'SMS',
      dataAprovacao: '2024-06-05',
      pesquisador: null,
      status: 'aguardando_distribuicao'
    },
    {
      id: 'PE 023/2024',
      objeto: 'Contratação de serviços de manutenção predial',
      secretaria: 'SMDU',
      dataAprovacao: '2024-06-04',
      pesquisador: 'Carla',
      status: 'em_andamento'
    },
    {
      id: 'CC 038/2024',
      objeto: 'Aquisição de medicamentos básicos',
      secretaria: 'SMS',
      dataAprovacao: '2024-06-03',
      pesquisador: 'Fernando',
      status: 'concluida'
    }
  ];

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'aguardando_distribuicao':
        return <Badge variant="outline" className="text-orange-600 border-orange-600">Aguardando Distribuição</Badge>;
      case 'em_andamento':
        return <Badge variant="outline" className="text-blue-600 border-blue-600">Em Andamento</Badge>;
      case 'concluida':
        return <Badge variant="outline" className="text-green-600 border-green-600">Concluída</Badge>;
      default:
        return <Badge variant="outline">Desconhecido</Badge>;
    }
  };

  if (modo === 'pesquisa' && processoSelecionado) {
    const processo = processosAguardando.find(p => p.id === processoSelecionado);
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => setModo('lista')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <h1 className="text-2xl font-bold">Pesquisa de Preços - {processoSelecionado}</h1>
        </div>
        
        <PesquisaPrecos
          processoId={processoSelecionado}
          pesquisador={processo?.pesquisador || 'Não atribuído'}
          onSalvar={(dados) => {
            console.log('Dados salvos:', dados);
            setModo('lista');
          }}
        />
      </div>
    );
  }

  if (modo === 'distribuir' && processoSelecionado) {
    const processo = processosAguardando.find(p => p.id === processoSelecionado);
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Button variant="outline" onClick={() => setModo('lista')}>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Voltar
          </Button>
          <h1 className="text-2xl font-bold">Distribuir Pesquisa de Preços</h1>
        </div>
        
        <DistribuirPesquisa
          processoId={processoSelecionado}
          processoObjeto={processo?.objeto || ''}
          onDistribuir={(pesquisadorId) => {
            alert(`✅ Pesquisa distribuída para: ${pesquisadorId}`);
            setModo('lista');
          }}
          onCancelar={() => setModo('lista')}
        />
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/pesquisa-precos">
            <Button variant="outline">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Sistema de Pesquisa de Preços</h1>
            <p className="text-muted-foreground mt-2">
              Sistema inteligente de pesquisa de preços via PNCP com IA
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <ExternalLink className="mr-1 h-3 w-3" />
            PNCP Integrado
          </Badge>
          <Badge variant="outline" className="text-xs">
            <Calculator className="mr-1 h-3 w-3" />
            IA Inteligente
          </Badge>
          <Button variant="outline">
            <Download className="mr-2 h-4 w-4" />
            Relatórios
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-orange-600">
              {processosAguardando.filter(p => p.status === 'aguardando_distribuicao').length}
            </div>
            <div className="text-sm text-muted-foreground">Aguardando Distribuição</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-blue-600">
              {processosAguardando.filter(p => p.status === 'em_andamento').length}
            </div>
            <div className="text-sm text-muted-foreground">Em Andamento</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-green-600">
              {processosAguardando.filter(p => p.status === 'concluida').length}
            </div>
            <div className="text-sm text-muted-foreground">Concluídas</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4">
            <div className="text-2xl font-bold text-purple-600">6</div>
            <div className="text-sm text-muted-foreground">Pesquisadores Ativos</div>
          </CardContent>
        </Card>
      </div>

      {/* Lista de Processos */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <FileText className="mr-2 h-5 w-5" />
            Processos para Pesquisa de Preços
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {processosAguardando.map((processo) => (
              <div key={processo.id} className="p-4 border rounded-lg">
                <div className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium">{processo.id}</h4>
                      {getStatusBadge(processo.status)}
                      <Badge variant="secondary">{processo.secretaria}</Badge>
                    </div>
                    <p className="text-sm text-muted-foreground mb-2">
                      {processo.objeto}
                    </p>
                    <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                      <span>📅 Aprovado: {new Date(processo.dataAprovacao).toLocaleDateString('pt-BR')}</span>
                      {processo.pesquisador && (
                        <span>👤 Pesquisador: {processo.pesquisador}</span>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {processo.status === 'aguardando_distribuicao' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setProcessoSelecionado(processo.id);
                          setModo('distribuir');
                        }}
                      >
                        <Users className="mr-2 h-4 w-4" />
                        Distribuir
                      </Button>
                    )}
                    
                    {processo.status === 'em_andamento' && (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => {
                          setProcessoSelecionado(processo.id);
                          setModo('pesquisa');
                        }}
                      >
                        <Search className="mr-2 h-4 w-4" />
                        Continuar Pesquisa
                      </Button>
                    )}
                    
                    {processo.status === 'concluida' && (
                      <Button variant="outline" size="sm">
                        <Download className="mr-2 h-4 w-4" />
                        Baixar Mapa
                      </Button>
                    )}
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Funcionalidades */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-blue-200 bg-blue-50">
          <CardHeader>
            <CardTitle className="text-blue-800">🤖 IA Inteligente</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2 text-blue-700">
              <li>✅ Busca refinada (álcool gel 500ml ≠ 300ml)</li>
              <li>✅ Filtros automáticos por especificação</li>
              <li>✅ Descarte de itens irrelevantes</li>
              <li>✅ Score de relevância 0-100</li>
              <li>✅ Extração automática do TR</li>
              <li>✅ Integração com sistema Thema</li>
            </ul>
          </CardContent>
        </Card>

        <Card className="border-green-200 bg-green-50">
          <CardHeader>
            <CardTitle className="text-green-800">📊 Análise Estatística</CardTitle>
          </CardHeader>
          <CardContent>
            <ul className="text-sm space-y-2 text-green-700">
              <li>✅ Cálculo automático de média e mediana</li>
              <li>✅ Aplicação do Decreto 9337/24 (±50%)</li>
              <li>✅ Pesquisa combinada (Art. 23 LF 14133/21)</li>
              <li>✅ Geração automática de mapas</li>
              <li>✅ Upload de planilhas via IA</li>
              <li>✅ Despachos padrão automáticos</li>
            </ul>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
