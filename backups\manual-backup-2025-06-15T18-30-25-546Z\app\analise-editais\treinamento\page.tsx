'use client';

import { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { 
  Upload, 
  Brain, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  FileText,
  Database,
  TrendingUp,
  Download,
  Trash2,
  Eye,
  Tag
} from 'lucide-react';

interface DocumentoTreinamento {
  id: string;
  nome: string;
  tipo: 'etp' | 'edital' | 'tr';
  categoria: 'aprovado' | 'reprovado' | 'conflito' | 'nao_classificado';
  objeto: string;
  secretaria: string;
  dataUpload: string;
  tamanho: number;
  observacoes?: string;
  problemas?: string[];
}

export default function TreinamentoMLPage() {
  const [documentos, setDocumentos] = useState<DocumentoTreinamento[]>([
    {
      id: '1',
      nome: 'ETP_Medicamentos_Saude_2024.pdf',
      tipo: 'etp',
      categoria: 'aprovado',
      objeto: 'Aquisição de medicamentos básicos',
      secretaria: 'Saúde',
      dataUpload: '2024-01-15',
      tamanho: 2048,
      observacoes: 'Documento modelo - bem estruturado'
    },
    {
      id: '2',
      nome: 'Edital_Racao_Agricultura_2024.pdf',
      tipo: 'edital',
      categoria: 'conflito',
      objeto: 'Compra de ração animal',
      secretaria: 'Agricultura',
      dataUpload: '2024-01-10',
      tamanho: 1536,
      problemas: ['Objeto conflitante com ETP de medicamentos', 'Habilitação incompleta']
    },
    {
      id: '3',
      nome: 'TR_Obras_Infraestrutura_2024.pdf',
      tipo: 'tr',
      categoria: 'reprovado',
      objeto: 'Pavimentação de ruas',
      secretaria: 'Infraestrutura',
      dataUpload: '2024-01-08',
      tamanho: 3072,
      problemas: ['Especificações técnicas inadequadas', 'Quantitativos não justificados']
    }
  ]);

  const [uploadingFiles, setUploadingFiles] = useState(false);
  const [selectedCategory, setSelectedCategory] = useState<string>('todos');

  const handleBulkUpload = async () => {
    setUploadingFiles(true);
    // Simular upload em lote
    setTimeout(() => {
      setUploadingFiles(false);
      alert('Upload em lote realizado com sucesso!');
    }, 2000);
  };

  const handleClassificarDocumento = (id: string, categoria: 'aprovado' | 'reprovado' | 'conflito') => {
    setDocumentos(prev => prev.map(doc => 
      doc.id === id ? { ...doc, categoria } : doc
    ));
  };

  const documentosFiltrados = selectedCategory === 'todos' 
    ? documentos 
    : documentos.filter(doc => doc.categoria === selectedCategory);

  const estatisticas = {
    total: documentos.length,
    aprovados: documentos.filter(d => d.categoria === 'aprovado').length,
    reprovados: documentos.filter(d => d.categoria === 'reprovado').length,
    conflitos: documentos.filter(d => d.categoria === 'conflito').length,
    naoClassificados: documentos.filter(d => d.categoria === 'nao_classificado').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Treinamento ML - CLMP</h1>
          <p className="text-muted-foreground mt-2">
            Sistema para treinar IA com documentos reais da Prefeitura de Mauá
          </p>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <Brain className="mr-1 h-3 w-3" />
            Machine Learning
          </Badge>
          <Badge variant="outline" className="text-xs">
            <Database className="mr-1 h-3 w-3" />
            Dataset CLMP
          </Badge>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{estatisticas.total}</div>
            <div className="text-sm text-muted-foreground">Total</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{estatisticas.aprovados}</div>
            <div className="text-sm text-muted-foreground">Aprovados</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-red-600">{estatisticas.reprovados}</div>
            <div className="text-sm text-muted-foreground">Reprovados</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{estatisticas.conflitos}</div>
            <div className="text-sm text-muted-foreground">Conflitos</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-gray-600">{estatisticas.naoClassificados}</div>
            <div className="text-sm text-muted-foreground">Não Class.</div>
          </CardContent>
        </Card>
      </div>

      {/* Upload em Lote */}
      <Card className="border-blue-200 bg-blue-50/50 dark:border-blue-800 dark:bg-blue-950/50">
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Upload className="mr-2 h-5 w-5" />
            Upload em Lote para Treinamento
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="text-center">
              <div className="text-sm font-medium mb-2">📄 ETPs</div>
              <Button variant="outline" className="w-full">
                <Upload className="mr-2 h-4 w-4" />
                Upload ETPs
              </Button>
            </div>
            
            <div className="text-center">
              <div className="text-sm font-medium mb-2">📋 Editais</div>
              <Button variant="outline" className="w-full">
                <Upload className="mr-2 h-4 w-4" />
                Upload Editais
              </Button>
            </div>
            
            <div className="text-center">
              <div className="text-sm font-medium mb-2">📝 TRs</div>
              <Button variant="outline" className="w-full">
                <Upload className="mr-2 h-4 w-4" />
                Upload TRs
              </Button>
            </div>
          </div>
          
          <div className="text-center pt-4 border-t">
            <Button 
              onClick={handleBulkUpload}
              size="lg"
              disabled={uploadingFiles}
              className="w-full max-w-md"
            >
              {uploadingFiles ? (
                <>
                  <Upload className="mr-2 h-4 w-4 animate-pulse" />
                  Processando...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  Upload em Lote (Múltiplos Arquivos)
                </>
              )}
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Filtros */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <Tag className="mr-2 h-5 w-5" />
            Classificação de Documentos
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex flex-wrap gap-2 mb-4">
            {[
              { key: 'todos', label: 'Todos', color: 'default' },
              { key: 'aprovado', label: 'Aprovados', color: 'default' },
              { key: 'reprovado', label: 'Reprovados', color: 'destructive' },
              { key: 'conflito', label: 'Conflitos', color: 'default' },
              { key: 'nao_classificado', label: 'Não Classificados', color: 'secondary' }
            ].map(filtro => (
              <Button
                key={filtro.key}
                variant={selectedCategory === filtro.key ? 'default' : 'outline'}
                size="sm"
                onClick={() => setSelectedCategory(filtro.key)}
              >
                {filtro.label}
              </Button>
            ))}
          </div>

          <div className="space-y-3">
            {documentosFiltrados.map(doc => (
              <div key={doc.id} className="p-4 border rounded-lg">
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-2">
                      <FileText className="h-4 w-4" />
                      <span className="font-medium">{doc.nome}</span>
                      <Badge variant="outline" className="text-xs">
                        {doc.tipo.toUpperCase()}
                      </Badge>
                      <Badge 
                        variant={
                          doc.categoria === 'aprovado' ? 'default' :
                          doc.categoria === 'reprovado' ? 'destructive' :
                          doc.categoria === 'conflito' ? 'default' : 'secondary'
                        }
                        className="text-xs"
                      >
                        {doc.categoria.replace('_', ' ').toUpperCase()}
                      </Badge>
                    </div>
                    
                    <div className="text-sm text-muted-foreground space-y-1">
                      <p><strong>Objeto:</strong> {doc.objeto}</p>
                      <p><strong>Secretaria:</strong> {doc.secretaria}</p>
                      <p><strong>Data:</strong> {doc.dataUpload} | <strong>Tamanho:</strong> {Math.round(doc.tamanho / 1024)} KB</p>
                      {doc.observacoes && (
                        <p className="text-green-600"><strong>Observações:</strong> {doc.observacoes}</p>
                      )}
                      {doc.problemas && (
                        <div className="text-red-600">
                          <strong>Problemas:</strong>
                          <ul className="list-disc list-inside ml-2">
                            {doc.problemas.map((problema, index) => (
                              <li key={index}>{problema}</li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2 ml-4">
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'aprovado')}
                    >
                      <CheckCircle className="h-4 w-4 text-green-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'reprovado')}
                    >
                      <XCircle className="h-4 w-4 text-red-600" />
                    </Button>
                    <Button
                      size="sm"
                      variant="outline"
                      onClick={() => handleClassificarDocumento(doc.id, 'conflito')}
                    >
                      <AlertTriangle className="h-4 w-4 text-orange-600" />
                    </Button>
                    <Button size="sm" variant="outline">
                      <Eye className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Progresso do Treinamento */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center text-lg">
            <TrendingUp className="mr-2 h-5 w-5" />
            Progresso do Treinamento ML
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div>
              <div className="flex justify-between text-sm mb-1">
                <span>Documentos Classificados</span>
                <span>{estatisticas.total - estatisticas.naoClassificados}/{estatisticas.total}</span>
              </div>
              <Progress value={((estatisticas.total - estatisticas.naoClassificados) / estatisticas.total) * 100} />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pt-4">
              <div>
                <h4 className="font-medium mb-2">📊 Qualidade do Dataset</h4>
                <ul className="text-sm space-y-1">
                  <li>✅ Documentos aprovados: {estatisticas.aprovados}</li>
                  <li>❌ Casos de erro: {estatisticas.reprovados}</li>
                  <li>⚠️ Conflitos identificados: {estatisticas.conflitos}</li>
                  <li>🎯 Recomendado: 15+ de cada categoria</li>
                </ul>
              </div>
              
              <div>
                <h4 className="font-medium mb-2">🚀 Próximos Passos</h4>
                <ul className="text-sm space-y-1">
                  <li>1. Classificar documentos restantes</li>
                  <li>2. Adicionar mais exemplos de cada tipo</li>
                  <li>3. Treinar modelo personalizado</li>
                  <li>4. Testar com novos documentos</li>
                </ul>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
