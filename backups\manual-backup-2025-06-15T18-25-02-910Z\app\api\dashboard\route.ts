import { NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

// Função para ler e processar dados usando o CSVReader
async function getDashboardData() {
  try {
    console.log('🔄 Carregando dados do dashboard usando CSVReader...');

    // Usar o mesmo método que as outras APIs
    const processos = await CSVReader.getAllProcessos();
    console.log('✅ Processos carregados:', processos.length);
    // Processar dados usando a mesma lógica das outras APIs
    const stats = {
      total: processos.length,
      modalidades: {} as Record<string, number>,
      secretarias: {} as Record<string, number>,
      status: {} as Record<string, number>
    };

    processos.forEach((processo: any) => {
      // Contar modalidades
      const modalidade = processo.MODALIDADE || 'Não informado';
      stats.modalidades[modalidade] = (stats.modalidades[modalidade] || 0) + 1;

      // Contar secretarias (requisitante)
      const requisitante = processo.REQUISITANTE || 'Não informado';
      stats.secretarias[requisitante] = (stats.secretarias[requisitante] || 0) + 1;

      // Contar status
      const status = processo.STATUS || 'Não informado';
      stats.status[status] = (stats.status[status] || 0) + 1;
    });

    console.log('📊 Estatísticas processadas:');
    console.log('📈 Total de processos:', stats.total);
    console.log('🏢 Secretarias únicas:', Object.keys(stats.secretarias).length);
    console.log('📋 Modalidades únicas:', Object.keys(stats.modalidades).length);
    console.log('📊 Status únicos:', Object.keys(stats.status).length);

    return {
      stats,
      records: processos
    };
  } catch (error) {
    console.error('❌ Erro ao carregar dados do dashboard:', error);
    return {
      stats: {
        total: 0,
        modalidades: {},
        secretarias: {},
        status: {}
      },
      records: []
    };
  }
}

export async function GET() {
  try {
    const data = await getDashboardData();
    return NextResponse.json(data);
  } catch (error) {
    console.error('Erro na API:', error);
    return NextResponse.json(
      { error: 'Erro ao carregar dados do dashboard' },
      { status: 500 }
    );
  }
}
