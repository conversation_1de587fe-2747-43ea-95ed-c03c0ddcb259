import { NextRequest, NextResponse } from 'next/server';

interface DespachoRequest {
  analiseId: string;
  tipoDespacho: 'SAJ' | 'SECRETARIA';
  analiseCompleta: any;
}

export async function POST(request: NextRequest) {
  try {
    const { analiseId, tipoDespacho, analiseCompleta } = await request.json() as DespachoRequest;

    if (!analiseId || !tipoDespacho || !analiseCompleta) {
      return NextResponse.json(
        { success: false, error: 'Dados insuficientes para gerar despacho' },
        { status: 400 }
      );
    }

    let despacho;

    if (tipoDespacho === 'SAJ') {
      despacho = gerarDespachoSAJ(analiseCompleta);
    } else {
      despacho = gerarDespachoSecretaria(analiseCompleta);
    }

    return NextResponse.json({
      success: true,
      data: {
        despachoId: `despacho_${Date.now()}`,
        timestamp: new Date().toISOString(),
        tipo: tipoDespacho,
        despacho
      }
    });

  } catch (error) {
    console.error('Erro na geração do despacho:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

function gerarDespachoSAJ(analise: any): any {
  const dataAtual = new Date().toLocaleDateString('pt-BR');
  
  return {
    cabecalho: {
      orgao: 'PREFEITURA MUNICIPAL DE MAUÁ',
      setor: 'CLMP - Coordenadoria de Licitações, Materiais e Patrimônio',
      destinatario: 'SAJ - Setor de Assessoria Jurídica',
      data: dataAtual,
      assunto: 'Encaminhamento para Análise e Parecer Jurídico'
    },
    
    corpo: {
      introducao: `Senhor(a) Assessor(a) Jurídico(a),

Encaminhamos a Vossa Senhoria o processo licitatório em epígrafe para análise jurídica e emissão de parecer, conforme determina a Lei Federal 14.133/21.`,

      analiseRealizada: `ANÁLISE TÉCNICA REALIZADA PELA CLMP:

✅ Score Geral de Conformidade: ${analise.scoreGeral}%
✅ Conformidade Lei 14.133/21: ${analise.analiseConformidade.scoreConformidade}%
✅ Análise por Inteligência Artificial: ${analise.analiseIA.scoreIA}%
✅ Confiança da Análise: ${Math.round(analise.analiseIA.confiancaGeral * 100)}%

STATUS: ${analise.status}`,

      documentosAnalisados: `DOCUMENTOS ANALISADOS:
- Estudo Técnico Preliminar (ETP)
- Edital de Licitação
- Termo de Referência (TR)

Todos os documentos foram submetidos à análise automatizada conforme checklist da Lei 14.133/21 e padrões da Prefeitura de Mauá.`,

      conformidadeLegal: analise.analiseConformidade.violacoesGraves?.length > 0 ? 
        `⚠️ ATENÇÃO: Foram identificadas ${analise.analiseConformidade.violacoesGraves.length} violação(ões) grave(s) da Lei 14.133/21 que requerem atenção jurídica especial.` :
        `✅ Não foram identificadas violações graves da Lei Federal 14.133/21.`,

      observacoes: analise.recomendacoes?.length > 0 ? 
        `OBSERVAÇÕES E RECOMENDAÇÕES:
${analise.recomendacoes.map((rec: any, index: number) => 
  `${index + 1}. ${rec.titulo}: ${rec.descricao}`
).join('\n')}` : 
        'Não há observações adicionais.',

      solicitacao: `SOLICITAÇÃO:

Solicitamos a análise jurídica do processo e emissão de parecer quanto à:
1. Conformidade com a Lei Federal 14.133/21
2. Adequação dos documentos licitatórios
3. Observância dos princípios da licitação
4. Eventuais riscos jurídicos identificados
5. Autorização para prosseguimento do certame

Prazo sugerido: 5 (cinco) dias úteis`,

      conclusao: `Permanecemos à disposição para esclarecimentos adicionais.

Atenciosamente,

CLMP - Coordenadoria de Licitações, Materiais e Patrimônio
Prefeitura Municipal de Mauá`
    },

    anexos: [
      'Relatório Completo de Análise Técnica',
      'Checklist de Conformidade Lei 14.133/21',
      'Análise de Riscos por Inteligência Artificial',
      'Documentos Licitatórios (ETP, Edital, TR)'
    ],

    metadados: {
      analiseId: analise.analiseId,
      scoreGeral: analise.scoreGeral,
      status: analise.status,
      dataAnalise: analise.timestamp,
      proximoPasso: 'AGUARDAR_PARECER_SAJ'
    }
  };
}

function gerarDespachoSecretaria(analise: any): any {
  const dataAtual = new Date().toLocaleDateString('pt-BR');
  const inconsistencias = analise.relatorioInconsistencias;
  
  return {
    cabecalho: {
      orgao: 'PREFEITURA MUNICIPAL DE MAUÁ',
      setor: 'CLMP - Coordenadoria de Licitações, Materiais e Patrimônio',
      destinatario: 'Secretaria Requisitante',
      data: dataAtual,
      assunto: 'Devolução para Adequação - Processo Licitatório'
    },
    
    corpo: {
      introducao: `Senhor(a) Secretário(a),

Após análise técnica realizada por esta Coordenadoria, o processo licitatório em epígrafe apresenta inconsistências que impedem o prosseguimento do certame.`,

      resultadoAnalise: `RESULTADO DA ANÁLISE:

❌ Score Geral: ${analise.scoreGeral}% (Mínimo: 80%)
📊 Conformidade Lei 14.133/21: ${analise.analiseConformidade.scoreConformidade}%
🤖 Análise IA: ${analise.analiseIA.scoreIA}%
📋 Total de Inconsistências: ${inconsistencias.totalInconsistencias}

STATUS: ${analise.status}`,

      inconsistenciasDetalhadas: `INCONSISTÊNCIAS IDENTIFICADAS:

${inconsistencias.categorias.map((cat: any, index: number) => 
  `${index + 1}. ${cat.titulo.toUpperCase()}
${cat.itens.map((item: any, i: number) => 
  `   ${i + 1}.${index + 1}. ${item.descricao || item.item}
        ${item.artigo ? `Fundamento: ${item.artigo}` : ''}
        ${item.correcao ? `Correção: ${item.correcao}` : ''}
        ${item.evidencia ? `Evidência: ${item.evidencia}` : ''}`
).join('\n')}`
).join('\n\n')}`,

      providencias: `PROVIDÊNCIAS NECESSÁRIAS:

${inconsistencias.observacoes.map((obs: string, index: number) => 
  `${index + 1}. ${obs}`
).join('\n')}`,

      prazos: `PRAZOS:

📅 Prazo para correção: ${inconsistencias.prazoCorrecao}
👤 Responsável: ${inconsistencias.responsavel}
🔄 Após correções: Reenviar para nova análise da CLMP`,

      orientacoes: `ORIENTAÇÕES IMPORTANTES:

• Todas as inconsistências devem ser corrigidas antes da resubmissão
• Consultar a assessoria jurídica para questões de conformidade legal
• Seguir rigorosamente os padrões da Prefeitura de Mauá
• Em caso de dúvidas, entrar em contato com a CLMP

A não observância das correções apontadas poderá resultar em nova devolução do processo.`,

      conclusao: `Permanecemos à disposição para esclarecimentos.

Atenciosamente,

CLMP - Coordenadoria de Licitações, Materiais e Patrimônio
Prefeitura Municipal de Mauá`
    },

    anexos: [
      'Relatório Detalhado de Inconsistências',
      'Checklist de Conformidade Lei 14.133/21',
      'Análise de Riscos Identificados',
      'Orientações para Adequação'
    ],

    metadados: {
      analiseId: analise.analiseId,
      scoreGeral: analise.scoreGeral,
      status: analise.status,
      totalInconsistencias: inconsistencias.totalInconsistencias,
      prazoRetorno: inconsistencias.prazoCorrecao,
      proximoPasso: 'AGUARDAR_CORRECOES_SECRETARIA'
    }
  };
}
