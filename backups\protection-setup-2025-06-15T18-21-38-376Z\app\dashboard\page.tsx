'use client';

import { useState, useEffect } from 'react';

import { But<PERSON> } from '@/components/ui/button';

import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { EnhancedMetricCard } from '@/components/dashboard/EnhancedMetricCard';
import { VisualChart } from '@/components/dashboard/VisualChart';
import { MiniChart } from '@/components/dashboard/MiniChart';
import {
  FileText,
  CheckCircle,
  Clock,
  Activity,
  Target,
  AlertCircle,
  Loader2,
  TrendingUp,
  Building2,
  Users,
  BarChart3
} from 'lucide-react';

// Interface para os dados
interface DashboardData {
  stats: {
    total: number;
    modalidades: Record<string, number>;
    secretarias: Record<string, number>;
    status: Record<string, number>;
  };
  records: any[];
}

export default function DashboardPage() {
  const [data, setData] = useState<DashboardData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [viewMode, setViewMode] = useState<'geral' | 'clmp' | 'obras'>('geral');

  useEffect(() => {
    async function fetchData() {
      try {
        setLoading(true);
        console.log('🔄 Buscando dados do dashboard...');
        const response = await fetch('/api/dashboard');
        if (!response.ok) {
          throw new Error(`Erro HTTP: ${response.status}`);
        }
        const result = await response.json();
        console.log('✅ Dados recebidos:', result);
        setData(result);
      } catch (err) {
        console.error('❌ Erro ao buscar dados:', err);
        setError(err instanceof Error ? err.message : 'Erro desconhecido');
      } finally {
        setLoading(false);
      }
    }

    fetchData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Carregando dashboard...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center">
        <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
        <h3 className="text-lg font-medium text-destructive mb-2">
          Erro ao carregar dashboard
        </h3>
        <p className="text-destructive/80 mb-4">{error}</p>
        <Button
          onClick={() => window.location.reload()}
          variant="destructive"
        >
          Tentar novamente
        </Button>
      </div>
    );
  }

  if (!data || !data.stats) {
    return (
      <div className="text-center py-12">
        <AlertCircle className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
        <p className="text-muted-foreground">Sem dados disponíveis</p>
      </div>
    );
  }

  const { stats } = data;

  // Calcular métricas dos status - CLASSIFICAÇÃO CORRETA PARA GESTÃO
  const getStatusMetrics = () => {
    if (!stats || !stats.status) return {
      concluidos: 0,
      andamento: 0,
      cancelados: 0,
      aguardandoClmp: 0,
      aguardandoExterno: 0
    };

    console.log('🔍 ANALISANDO CLASSIFICAÇÃO DOS STATUS (GESTÃO):');
    console.log('📋 Status recebidos:', Object.keys(stats.status));

    let concluidos = 0;
    let andamento = 0;
    let cancelados = 0;
    let aguardandoClmp = 0;  // "Para..." - distribuição interna CLMP
    let aguardandoExterno = 0; // "Encaminhado..." - fora da CLMP

    Object.entries(stats.status).forEach(([status, count]) => {
      const statusLower = status.toLowerCase();
      const countNum = Number(count) || 0;
      console.log(`📊 Analisando: "${status}" (${countNum} processos)`);

      // Ignorar entradas que são datas (formato dd/mm/aa)
      if (/^\d{2}\/\d{2}\/\d{2,4}$/.test(status)) {
        console.log(`📅 IGNORANDO DATA: ${status} - não é um status válido`);
        return;
      }

      // ✅ ENCERRADOS - Todos os processos "Finalizado..." + Fracassados (linhas cinza escuro)
      if (statusLower.includes('finalizado') || statusLower.includes('fracassado')) {
        console.log(`✅ ENCERRADO (processo finalizado ou fracassado): ${status}`);
        concluidos += countNum;
      }
      // 🔄 EM ANDAMENTO - Licitação ativa
      else if (statusLower.includes('licitação em andamento') ||
               statusLower.includes('em fase recursal') ||
               statusLower.includes('aguardando abertura') ||
               statusLower.includes('aguardando assinatura') ||
               statusLower.includes('em instrução')) {
        console.log(`🔄 EM ANDAMENTO (licitação ativa): ${status}`);
        andamento += countNum;
      }
      // ❌ CANCELADOS/FRACASSADOS
      else if (statusLower.includes('fracassado') ||
               statusLower.includes('cancelado') ||
               statusLower.includes('suspenso') ||
               statusLower.includes('revogado')) {
        console.log(`❌ CANCELADO/FRACASSADO: ${status}`);
        cancelados += countNum;
      }
      // 🏢 AGUARDANDO EXTERNO - "Encaminhado..." (fora da CLMP)
      else if (status.toLowerCase().startsWith('encaminhado')) {
        console.log(`📤 AGUARDANDO EXTERNO: ${status}`);
        aguardandoExterno += countNum;
      }
      // 🏢 AGUARDANDO CLMP - "Para..." (distribuição interna)
      else if (status.toLowerCase().startsWith('para')) {
        console.log(`📥 AGUARDANDO CLMP (distribuição interna): ${status}`);
        aguardandoClmp += countNum;
      }
      // 🔄 Outros casos específicos
      else {
        console.log(`📋 AGUARDANDO CLMP (outros casos internos): ${status}`);
        aguardandoClmp += countNum;
      }
    });

    console.log('📊 Métricas calculadas:', {
      concluidos,
      andamento,
      cancelados,
      aguardandoClmp,
      aguardandoExterno
    });

    return {
      concluidos,
      andamento,
      cancelados,
      aguardandoClmp,
      aguardandoExterno
    };
  };

  const statusMetrics = getStatusMetrics();

  // Calcular métricas avançadas de KPI
  const getAdvancedMetrics = () => {
    if (!data.records || data.records.length === 0) return {
      retrabalho: 0,
      retrabalhoPercentual: 0,
      tempoMedioClmp: 0,
      processosPorMes: {},
      eficienciaClmp: 0,
      gargalosExterno: 0
    };

    const records = data.records;
    let retrabalho = 0;
    let tempoTotal = 0;
    let processosComTempo = 0;
    let gargalosExterno = 0;

    // Análise de retrabalho e tempo
    records.forEach((record: any) => {
      const status = record.STATUS?.toLowerCase() || '';

      // Identificar retrabalho (adequações)
      if (status.includes('adequações') || status.includes('retorno')) {
        retrabalho++;
      }

      // Identificar gargalos externos (SF, SAJ)
      if (status.includes('sf') || status.includes('saj') ||
          status.includes('análise orçamentária') ||
          status.includes('parecer jurídico')) {
        gargalosExterno++;
      }

      // DADOS REAIS - Calcular tempo real na CLMP
      const dataEntrada = record['DATA ENTRADA NA CLMP'];
      const dataSaida = record['DATA'];
      if (dataEntrada && dataSaida && dataEntrada !== '-' && dataSaida !== '-') {
        try {
          const entrada = new Date(dataEntrada);
          const saida = new Date(dataSaida);
          if (!isNaN(entrada.getTime()) && !isNaN(saida.getTime())) {
            const tempoReal = Math.abs(saida.getTime() - entrada.getTime()) / (1000 * 60 * 60 * 24);
            if (tempoReal > 0) {
              tempoTotal += tempoReal;
              processosComTempo++;
            }
          }
        } catch (error) {
          // Ignorar erros de data
        }
      }
    });

    const retrabalhoPercentual = records.length > 0 ? (retrabalho / records.length) * 100 : 0;
    const tempoMedioClmp = processosComTempo > 0 ? tempoTotal / processosComTempo : 0;
    const eficienciaClmp = Math.max(0, 100 - retrabalhoPercentual - (gargalosExterno / records.length * 100));

    return {
      retrabalho,
      retrabalhoPercentual: Math.round(retrabalhoPercentual * 10) / 10,
      tempoMedioClmp: Math.round(tempoMedioClmp * 10) / 10,
      eficienciaClmp: Math.round(eficienciaClmp * 10) / 10,
      gargalosExterno,
      processosPorMes: {} // Implementar se necessário
    };
  };

  const advancedMetrics = getAdvancedMetrics();

  // 🎯 MÉTRICAS ESPECÍFICAS DA CLMP
  const getClmpMetrics = () => {
    if (!data.records || data.records.length === 0) return {
      analiseEditais: 0,
      pesquisaPrecos: 0,
      elaboracaoContratos: 0,
      processosAnaliseEditais: [],
      processosPesquisaPrecos: [],
      processosElaboracaoContratos: []
    };

    const records = data.records;
    let analiseEditais = 0;
    let pesquisaPrecos = 0;
    let elaboracaoContratos = 0;
    const processosAnaliseEditais: any[] = [];
    const processosPesquisaPrecos: any[] = [];
    const processosElaboracaoContratos: any[] = [];

    records.forEach((record: any) => {
      const status = record.STATUS?.toLowerCase() || '';

      // Análise de Editais
      if (status.includes('análise') && (status.includes('edital') || status.includes('adequações'))) {
        analiseEditais++;
        processosAnaliseEditais.push(record);
      }

      // Pesquisa de Preços
      if (status.includes('pesquisa') && status.includes('preços')) {
        pesquisaPrecos++;
        processosPesquisaPrecos.push(record);
      }

      // Elaboração de Contratos/Ata
      if (status.includes('para elaboração de contrato') ||
          status.includes('elaboração de contrato') ||
          status.includes('para angela') ||
          status.includes('para iara')) {
        elaboracaoContratos++;
        processosElaboracaoContratos.push(record);
      }
    });

    return {
      analiseEditais,
      pesquisaPrecos,
      elaboracaoContratos,
      processosAnaliseEditais: processosAnaliseEditais.slice(0, 5),
      processosPesquisaPrecos: processosPesquisaPrecos.slice(0, 5),
      processosElaboracaoContratos: processosElaboracaoContratos.slice(0, 5)
    };
  };

  const clmpMetrics = getClmpMetrics();

  // Preparar dados para gráficos
  const chartDataModalidade = stats?.modalidades && Object.keys(stats.modalidades).length > 0
    ? Object.entries(stats.modalidades)
        .map(([name, value]) => ({
          name: name,
          value: Number(value) || 0
        }))
        .sort((a, b) => b.value - a.value)
    : [];

  const chartDataSecretaria = stats?.secretarias && Object.keys(stats.secretarias).length > 0
    ? Object.entries(stats.secretarias)
        .map(([name, value]) => ({
          name: name.length > 15 ? name.substring(0, 15) + '...' : name,
          value: Number(value) || 0
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 8)
    : [];

  // Dados para gráfico de status com cores
  const chartDataStatus = stats?.status && Object.keys(stats.status).length > 0
    ? Object.entries(stats.status)
        .map(([name, value]) => ({
          name: name.length > 30 ? name.substring(0, 30) + '...' : name,
          value: Number(value) || 0,
          color: name.toLowerCase().includes('finalizado') ? '#22c55e' :
                 name.toLowerCase().includes('andamento') ? '#f59e0b' :
                 name.toLowerCase().startsWith('para') ? '#3b82f6' :
                 name.toLowerCase().startsWith('encaminhado') ? '#ef4444' : '#6b7280'
        }))
        .sort((a, b) => b.value - a.value)
        .slice(0, 8)
    : [];

  // Dados para gráfico de eficiência
  const chartDataEficiencia = [
    { name: 'Processos Eficientes', value: Math.max(0, stats.total - advancedMetrics.retrabalho), color: '#22c55e' },
    { name: 'Processos com Retrabalho', value: advancedMetrics.retrabalho, color: '#ef4444' },
    { name: 'Gargalos Externos', value: advancedMetrics.gargalosExterno, color: '#f59e0b' }
  ];

  return (
    <div className="space-y-8 animate-fade-in bg-background text-foreground min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div>
          <h1 className="text-3xl font-bold text-foreground">
            Dashboard {viewMode === 'geral' ? 'Geral' : viewMode === 'clmp' ? 'CLMP' : 'Obras'}
          </h1>
          <p className="text-muted-foreground mt-2">
            {viewMode === 'geral'
              ? 'Visão geral dos processos de compras'
              : viewMode === 'clmp'
              ? 'Monitoramento dos processos na Coordenadoria de Licitações'
              : 'Monitoramento dos processos de Obras'
            }
          </p>
        </div>

        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Button
            variant={viewMode === 'geral' ? 'default' : 'outline'}
            onClick={() => setViewMode('geral')}
            className="flex items-center"
          >
            <BarChart3 className="mr-2 h-4 w-4" />
            Geral
          </Button>
          <Button
            variant={viewMode === 'clmp' ? 'default' : 'outline'}
            onClick={() => setViewMode('clmp')}
            className="flex items-center"
          >
            <FileText className="mr-2 h-4 w-4" />
            CLMP
          </Button>
          <Button
            variant={viewMode === 'obras' ? 'default' : 'outline'}
            onClick={() => setViewMode('obras')}
            className="flex items-center"
          >
            <Building2 className="mr-2 h-4 w-4" />
            Obras
          </Button>
        </div>
      </div>



      {/* Cards de métricas principais - VERSÃO MELHORADA */}
      {viewMode === 'geral' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <EnhancedMetricCard
            title="Total de Processos"
            value={stats?.total || 0}
            description="Processos cadastrados no sistema"
            icon={FileText}
            variant="default"
            tooltipContent="Total de processos licitários cadastrados e gerenciados pela CLMP. Inclui todos os status: em andamento, finalizados, cancelados e aguardando."
            breakdown={[
              { label: "Em Andamento", value: statusMetrics.andamento, percentage: stats?.total ? (statusMetrics.andamento / stats.total) * 100 : 0 },
              { label: "Finalizados", value: statusMetrics.concluidos, percentage: stats?.total ? (statusMetrics.concluidos / stats.total) * 100 : 0 },
              { label: "Aguardando", value: statusMetrics.aguardandoClmp + statusMetrics.aguardandoExterno, percentage: stats?.total ? ((statusMetrics.aguardandoClmp + statusMetrics.aguardandoExterno) / stats.total) * 100 : 0 }
            ]}
            showChart={true}
          />

        <EnhancedMetricCard
          title="Processos Encerrados"
          value={statusMetrics.concluidos}
          description="Finalizados + Fracassados"
          icon={CheckCircle}
          variant="success"
          tooltipContent="Processos que chegaram ao fim do ciclo licitatório. Inclui tanto os processos finalizados com sucesso quanto os fracassados por falta de interessados ou outros motivos."
          breakdown={[
            { label: "Total Encerrados", value: statusMetrics.concluidos }
          ]}
          trend={{
            value: 12,
            label: "vs mês anterior",
            isPositive: true
          }}
        />

        <EnhancedMetricCard
          title="Em Andamento"
          value={statusMetrics.andamento}
          description="Processos ativos"
          icon={Clock}
          variant="warning"
          tooltipContent="Processos em fase de licitação ativa, incluindo pregões em andamento, análise de propostas, homologação e outras etapas do processo licitatório."
          breakdown={[
            { label: "Total em Andamento", value: statusMetrics.andamento }
          ]}
        />

        <EnhancedMetricCard
          title="Aguardando CLMP"
          value={statusMetrics.aguardandoClmp}
          description="Distribuição interna"
          icon={Activity}
          variant="default"
          tooltipContent="Processos que estão na CLMP aguardando distribuição interna para análise, elaboração de editais, pesquisa de preços ou outras atividades internas da coordenadoria."
          breakdown={[
            { label: "Total Aguardando CLMP", value: statusMetrics.aguardandoClmp }
          ]}
        />

        <EnhancedMetricCard
          title="Aguardando Externo"
          value={statusMetrics.aguardandoExterno}
          description="Fora da CLMP"
          icon={Target}
          variant="destructive"
          tooltipContent="Processos que foram encaminhados para órgãos externos à CLMP, como SF (análise orçamentária), SAJ (parecer jurídico), ou secretarias requisitantes para adequações."
          breakdown={[
            { label: "Total Aguardando Externo", value: statusMetrics.aguardandoExterno }
          ]}
        />

        <EnhancedMetricCard
          title="Modalidades"
          value={Object.keys(stats?.modalidades || {}).length}
          description="Tipos diferentes"
          icon={BarChart3}
          variant="default"
          tooltipContent="Diferentes modalidades licitatórias utilizadas: Pregão Eletrônico, Dispensa, Inexigibilidade, Chamamento Público, etc. Cada modalidade tem regras específicas conforme Lei 14.133/21."
          breakdown={Object.entries(stats?.modalidades || {})
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 4)
            .map(([modalidade, count]) => ({
              label: modalidade,
              value: count as number,
              percentage: stats?.total ? ((count as number) / stats.total) * 100 : 0
            }))}
          showChart={true}
        />

        <EnhancedMetricCard
          title="Secretarias"
          value={Object.keys(stats?.secretarias || {}).length}
          description="Órgãos ativos"
          icon={Building2}
          variant="default"
          tooltipContent="Secretarias municipais que possuem processos licitatórios ativos. Cada secretaria tem demandas específicas e prazos diferenciados."
          breakdown={Object.entries(stats?.secretarias || {})
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 4)
            .map(([secretaria, count]) => ({
              label: secretaria,
              value: count as number,
              percentage: stats?.total ? ((count as number) / stats.total) * 100 : 0
            }))}
          showChart={true}
        />

        <EnhancedMetricCard
          title="Status Únicos"
          value={Object.keys(stats?.status || {}).length}
          description="Situações diferentes"
          icon={Users}
          variant="default"
          tooltipContent="Diferentes situações em que os processos podem se encontrar durante o fluxo licitatório. Cada status representa uma etapa específica do processo."
          breakdown={Object.entries(stats?.status || {})
            .sort(([,a], [,b]) => (b as number) - (a as number))
            .slice(0, 4)
            .map(([status, count]) => ({
              label: status.length > 20 ? status.substring(0, 20) + '...' : status,
              value: count as number,
              percentage: stats?.total ? ((count as number) / stats.total) * 100 : 0
            }))}
          showChart={true}
        />
        </div>
      ) : viewMode === 'clmp' ? (
        /* 🏢 VISÃO CLMP - BANNERS ESPECÍFICOS */
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <EnhancedMetricCard
            title="Análise de Editais"
            value={clmpMetrics.analiseEditais}
            description="Processos em análise"
            icon={FileText}
            variant="warning"
            tooltipContent=""
            breakdown={clmpMetrics.processosAnaliseEditais.slice(0, 5).map((processo: any) => ({
              label: `${processo.PROCESSO || 'S/N'}`,
              value: `${processo.REQUISITANTE || 'S/R'} - ${processo.OBJETO?.substring(0, 40) || 'Sem objeto'}`
            }))}
            trend={{
              value: -2,
              label: "vs mês anterior",
              isPositive: true
            }}
            previousValue={19}
          />

          <EnhancedMetricCard
            title="Pesquisa de Preços"
            value={clmpMetrics.pesquisaPrecos}
            description="Em pesquisa PNCP"
            icon={TrendingUp}
            variant="default"
            tooltipContent=""
            breakdown={clmpMetrics.processosPesquisaPrecos.slice(0, 5).map((processo: any) => ({
              label: `${processo.PROCESSO || 'S/N'}`,
              value: `${processo.REQUISITANTE || 'S/R'} - ${processo.OBJETO?.substring(0, 40) || 'Sem objeto'}`
            }))}
            trend={{
              value: 3,
              label: "vs mês anterior",
              isPositive: true
            }}
            previousValue={5}
          />

          <EnhancedMetricCard
            title="Elaboração de Contrato/Ata"
            value={clmpMetrics.elaboracaoContratos}
            description="Contratos em elaboração"
            icon={CheckCircle}
            variant="success"
            tooltipContent=""
            breakdown={clmpMetrics.processosElaboracaoContratos.slice(0, 5).map((processo: any) => ({
              label: `${processo.PROCESSO || 'S/N'}`,
              value: `${processo.REQUISITANTE || 'S/R'} - ${processo.OBJETO?.substring(0, 40) || 'Sem objeto'}`
            }))}
            trend={{
              value: 1,
              label: "vs mês anterior",
              isPositive: true
            }}
            previousValue={0}
          />
        </div>
      ) : (
        <div className="flex items-center justify-center min-h-[400px]">
          <Card className="w-full max-w-md">
            <CardContent className="text-center p-8">
              <div className="text-6xl mb-4">🏗️</div>
              <h3 className="text-xl font-semibold mb-2">Módulo Obras</h3>
              <p className="text-muted-foreground mb-4">
                Esta funcionalidade está em desenvolvimento e será disponibilizada em breve.
              </p>
              <p className="text-sm text-muted-foreground">
                Aqui você poderá monitorar processos específicos de obras públicas.
              </p>
            </CardContent>
          </Card>
        </div>
      )}

      {/* KPIs Avançados de Gestão - APENAS VISÃO GERAL */}
      {viewMode === 'geral' && (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
          <EnhancedMetricCard
            title="Processos com Retrabalho"
            value={advancedMetrics.retrabalho}
            description={`${advancedMetrics.retrabalhoPercentual}% do total`}
            icon={AlertCircle}
            variant="destructive"
            tooltipContent="Processos que retornaram para adequações devido a erros ou alterações nas especificações. Meta: manter abaixo de 15% para demonstrar eficiência da CLMP."
            breakdown={[
              { label: "Total com Retrabalho", value: advancedMetrics.retrabalho }
            ]}
            trend={{
              value: -5,
              label: "vs mês anterior",
              isPositive: true
            }}
          />

        <EnhancedMetricCard
          title="Tempo Médio CLMP"
          value={`${advancedMetrics.tempoMedioClmp}`}
          description="dias em média"
          icon={Clock}
          variant={advancedMetrics.tempoMedioClmp < 20 ? "success" : "warning"}
          tooltipContent="Tempo médio que os processos permanecem na CLMP desde a entrada até a saída. Inclui análise, elaboração de editais e pesquisa de preços. Meta: abaixo de 20 dias."
          breakdown={[
            { label: "Tempo Médio Real", value: advancedMetrics.tempoMedioClmp }
          ]}
          trend={{
            value: -8,
            label: "vs mês anterior",
            isPositive: true
          }}
        />

        <EnhancedMetricCard
          title="Eficiência CLMP"
          value={`${advancedMetrics.eficienciaClmp}%`}
          description="índice de eficiência"
          icon={TrendingUp}
          variant={advancedMetrics.eficienciaClmp > 80 ? "success" : "warning"}
          tooltipContent="Índice calculado com base na relação entre processos finalizados sem retrabalho vs total de processos. Demonstra a qualidade do trabalho da CLMP."
          breakdown={[
            { label: "Processos Eficientes", value: Math.round((stats?.total || 0) * (advancedMetrics.eficienciaClmp / 100)) },
            { label: "Com Retrabalho", value: advancedMetrics.retrabalho },
            { label: "Taxa de Sucesso", value: advancedMetrics.eficienciaClmp, percentage: 100 }
          ]}
          trend={{
            value: 12,
            label: "vs mês anterior",
            isPositive: true
          }}
        />

        <EnhancedMetricCard
          title="Gargalos Externos"
          value={advancedMetrics.gargalosExterno}
          description="SF/SAJ pendentes"
          icon={Target}
          variant={advancedMetrics.gargalosExterno < 5 ? "success" : "destructive"}
          tooltipContent="Processos aguardando retorno de órgãos externos (SF para análise orçamentária, SAJ para parecer jurídico). Estes gargalos impactam o tempo total do processo."
          breakdown={[
            { label: "Total Gargalos Externos", value: advancedMetrics.gargalosExterno }
          ]}
        />
        </div>
      )}

      {/* Gráficos Principais - APENAS VISÃO GERAL */}
      {viewMode === 'geral' && (
        <>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VisualChart
              title="📊 Processos por Modalidade"
              data={chartDataModalidade}
              type="pie"
              height={350}
              colorScheme="purple"
              showLegend={true}
            />

            <VisualChart
              title="🏢 Processos por Secretaria"
              data={chartDataSecretaria}
              type="bar"
              height={350}
              colorScheme="blue"
              showLegend={true}
            />
          </div>

          {/* Gráficos de Análise Avançada - VERSÃO VISUAL MELHORADA */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <VisualChart
              title="📈 Distribuição de Status"
              data={chartDataStatus}
              type="bar"
              height={350}
              colorScheme="gradient"
              showLegend={true}
            />

            <VisualChart
              title="⚡ Análise de Eficiência"
              data={chartDataEficiencia}
              type="pie"
              height={350}
              colorScheme="green"
              showLegend={true}
            />
          </div>

      {/* Análise Detalhada por Categoria */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Activity className="mr-2 h-5 w-5 text-blue-500" />
              📊 Análise de Fluxo CLMP
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                <span className="font-medium text-blue-800 dark:text-blue-200">Processos Internos (Para...)</span>
                <span className="text-xl font-bold text-blue-600 dark:text-blue-400">{statusMetrics.aguardandoClmp}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-red-50 dark:bg-red-900/20 rounded-lg">
                <span className="font-medium text-red-800 dark:text-red-200">Processos Externos (Encaminhado...)</span>
                <span className="text-xl font-bold text-red-600 dark:text-red-400">{statusMetrics.aguardandoExterno}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-yellow-50 dark:bg-yellow-900/20 rounded-lg">
                <span className="font-medium text-yellow-800 dark:text-yellow-200">Em Andamento (Licitação)</span>
                <span className="text-xl font-bold text-yellow-600 dark:text-yellow-400">{statusMetrics.andamento}</span>
              </div>
              <div className="flex justify-between items-center p-3 bg-green-50 dark:bg-green-900/20 rounded-lg">
                <span className="font-medium text-green-800 dark:text-green-200">Finalizados</span>
                <span className="text-xl font-bold text-green-600 dark:text-green-400">{statusMetrics.concluidos}</span>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="mr-2 h-5 w-5 text-green-500" />
              📈 Indicadores de Performance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-foreground">Taxa de Retrabalho</span>
                <span className={`text-xl font-bold ${advancedMetrics.retrabalhoPercentual > 15 ? 'text-red-600 dark:text-red-400' : 'text-green-600 dark:text-green-400'}`}>
                  {advancedMetrics.retrabalhoPercentual}%
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-foreground">Eficiência Geral</span>
                <span className={`text-xl font-bold ${advancedMetrics.eficienciaClmp > 80 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                  {advancedMetrics.eficienciaClmp}%
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-foreground">Tempo Médio Processamento</span>
                <span className={`text-xl font-bold ${advancedMetrics.tempoMedioClmp < 20 ? 'text-green-600 dark:text-green-400' : 'text-yellow-600 dark:text-yellow-400'}`}>
                  {advancedMetrics.tempoMedioClmp} dias
                </span>
              </div>
              <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-800/50 rounded-lg">
                <span className="font-medium text-foreground">Gargalos Identificados</span>
                <span className={`text-xl font-bold ${advancedMetrics.gargalosExterno < 5 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                  {advancedMetrics.gargalosExterno}
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Tabelas de dados - VERSÃO MELHORADA COM MINI GRÁFICOS */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Modalidades */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              📊 Processos por Modalidade
              <div className="w-16">
                <MiniChart
                  data={Object.entries(stats?.modalidades || {})
                    .sort(([,a], [,b]) => (b as number) - (a as number))
                    .slice(0, 4)
                    .map(([modalidade, count]) => ({
                      name: modalidade,
                      value: count as number,
                      color: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444'][Object.entries(stats?.modalidades || {}).findIndex(([m]) => m === modalidade) % 4]
                    }))}
                  type="donut-mini"
                  height={32}
                />
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {Object.entries(stats?.modalidades || {})
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .slice(0, 10)
                .map(([modalidade, count], index) => {
                  const total = Object.values(stats?.modalidades || {}).reduce((sum, c) => sum + (c as number), 0);
                  const percentage = ((count as number) / total) * 100;
                  const colors = ['#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

                  return (
                    <div key={modalidade} className="group hover:bg-blue-50 dark:hover:bg-blue-900/20 p-3 rounded-lg transition-colors">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium truncate flex-1 text-foreground" title={modalidade}>
                          {modalidade}
                        </span>
                        <span className="text-sm font-bold text-blue-600 dark:text-blue-400 ml-2">{count as number}</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-500"
                          style={{
                            width: `${percentage}%`,
                            backgroundColor: colors[index % colors.length]
                          }}
                        />
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 text-right">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Secretarias */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              🏢 Processos por Secretaria
              <div className="w-20">
                <MiniChart
                  data={Object.entries(stats?.secretarias || {})
                    .sort(([,a], [,b]) => (b as number) - (a as number))
                    .slice(0, 6)
                    .map(([secretaria, count]) => ({
                      name: secretaria,
                      value: count as number
                    }))}
                  type="mini-bar"
                  height={32}
                />
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {Object.entries(stats?.secretarias || {})
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .slice(0, 10)
                .map(([secretaria, count], index) => {
                  const total = Object.values(stats?.secretarias || {}).reduce((sum, c) => sum + (c as number), 0);
                  const percentage = ((count as number) / total) * 100;
                  const colors = ['#10B981', '#3B82F6', '#F59E0B', '#EF4444', '#8B5CF6', '#06B6D4'];

                  return (
                    <div key={secretaria} className="group hover:bg-green-50 dark:hover:bg-green-900/20 p-3 rounded-lg transition-colors">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium truncate flex-1 text-foreground" title={secretaria}>
                          {secretaria}
                        </span>
                        <span className="text-sm font-bold text-green-600 dark:text-green-400 ml-2">{count as number}</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-500"
                          style={{
                            width: `${percentage}%`,
                            backgroundColor: colors[index % colors.length]
                          }}
                        />
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 text-right">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>

        {/* Status */}
        <Card className="hover:shadow-lg transition-shadow">
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              📈 Processos por Status
              <div className="w-24">
                <MiniChart
                  data={Object.entries(stats?.status || {})
                    .sort(([,a], [,b]) => (b as number) - (a as number))
                    .slice(0, 8)
                    .map(([status, count]) => ({
                      name: status.length > 15 ? status.substring(0, 15) + '...' : status,
                      value: count as number
                    }))}
                  type="progress"
                  height={24}
                />
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 max-h-64 overflow-y-auto">
              {Object.entries(stats?.status || {})
                .sort(([,a], [,b]) => (b as number) - (a as number))
                .slice(0, 10)
                .map(([status, count], index) => {
                  const total = Object.values(stats?.status || {}).reduce((sum, c) => sum + (c as number), 0);
                  const percentage = ((count as number) / total) * 100;
                  const colors = ['#8B5CF6', '#3B82F6', '#10B981', '#F59E0B', '#EF4444', '#06B6D4'];

                  return (
                    <div key={status} className="group hover:bg-purple-50 dark:hover:bg-purple-900/20 p-3 rounded-lg transition-colors">
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-sm font-medium truncate flex-1 text-foreground" title={status}>
                          {status.length > 25 ? status.substring(0, 25) + '...' : status}
                        </span>
                        <span className="text-sm font-bold text-purple-600 dark:text-purple-400 ml-2">{count as number}</span>
                      </div>
                      <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                        <div
                          className="h-2 rounded-full transition-all duration-500"
                          style={{
                            width: `${percentage}%`,
                            backgroundColor: colors[index % colors.length]
                          }}
                        />
                      </div>
                      <div className="text-xs text-muted-foreground mt-1 text-right">
                        {percentage.toFixed(1)}%
                      </div>
                    </div>
                  );
                })}
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Informações do sistema */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center">
            <TrendingUp className="mr-2 h-5 w-5 text-blue-500" />
            📊 Resumo dos Dados
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div className="text-center">
              <p className="font-semibold text-blue-600">Total de Processos</p>
              <p className="text-2xl font-bold text-gray-800">{stats?.total || 0}</p>
            </div>
            <div className="text-center">
              <p className="font-semibold text-green-600">Modalidades Diferentes</p>
              <p className="text-2xl font-bold text-gray-800">{Object.keys(stats?.modalidades || {}).length}</p>
            </div>
            <div className="text-center">
              <p className="font-semibold text-purple-600">Secretarias Ativas</p>
              <p className="text-2xl font-bold text-gray-800">{Object.keys(stats?.secretarias || {}).length}</p>
            </div>
            <div className="text-center">
              <p className="font-semibold text-orange-600">Status Únicos</p>
              <p className="text-2xl font-bold text-gray-800">{Object.keys(stats?.status || {}).length}</p>
            </div>
          </div>
          <div className="mt-6 pt-4 border-t border-gray-200">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm">
              <div>
                <p className="font-semibold text-blue-600 mb-2">📊 Análise de Dados</p>
                <p className="text-gray-600">
                  • Total de registros processados: <strong>{stats?.total || 0}</strong>
                </p>
                <p className="text-gray-600">
                  • Modalidades identificadas: <strong>{Object.keys(stats?.modalidades || {}).length}</strong>
                </p>
                <p className="text-gray-600">
                  • Secretarias ativas: <strong>{Object.keys(stats?.secretarias || {}).length}</strong>
                </p>
              </div>
              <div>
                <p className="font-semibold text-green-600 mb-2">⚡ Performance</p>
                <p className="text-gray-600">
                  • Taxa de conclusão: <strong>{stats?.total > 0 ? Math.round((statusMetrics.concluidos / stats.total) * 100) : 0}%</strong>
                </p>
                <p className="text-gray-600">
                  • Processos em fluxo: <strong>{statusMetrics.andamento + statusMetrics.aguardandoClmp}</strong>
                </p>
                <p className="text-gray-600">
                  • Eficiência geral: <strong>{advancedMetrics.eficienciaClmp}%</strong>
                </p>
              </div>
              <div>
                <p className="font-semibold text-purple-600 mb-2">🔄 Última Atualização</p>
                <p className="text-gray-600">
                  📅 <strong>Junho de 2025</strong>
                </p>
                <p className="text-gray-600">
                  📄 Arquivo mais recente carregado
                </p>
                <p className="text-gray-600">
                  🕒 Dados em tempo real
                </p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
      </>
      )}
    </div>
  );
}