export interface Usuario {
  id: string;
  nome: string;
  email: string;
  telefone?: string;
  secretaria: string;
  cargo: string;
  perfil: PerfilUsuario;
  status: 'ativo' | 'inativo' | 'bloqueado';
  ultimoAcesso?: string;
  dataCriacao: string;
  permissoes: Permissao[];
  configuracoes?: ConfiguracaoUsuario;
}

export type PerfilUsuario = 
  | 'admin'           // Acesso total ao sistema
  | 'gestor'          // Acesso a sua secretaria + relatórios
  | 'operador'        // Acesso básico a processos
  | 'consulta'        // Apenas visualização
  | 'isabela'         // Perfil específico para Isabela (cadastro de processos)
  | 'tath';           // Perfil específico para Tath (cadastro de processos)

export type Permissao = 
  | 'processos.criar'
  | 'processos.editar'
  | 'processos.visualizar'
  | 'processos.excluir'
  | 'contratos.criar'
  | 'contratos.editar'
  | 'contratos.visualizar'
  | 'contratos.excluir'
  | 'usuarios.criar'
  | 'usuarios.editar'
  | 'usuarios.visualizar'
  | 'usuarios.excluir'
  | 'relatorios.gerar'
  | 'relatorios.exportar'
  | 'configuracoes.sistema'
  | 'configuracoes.secretaria'
  | 'prioridades.classificar'
  | 'alertas.gerenciar'
  | 'pesquisa.precos'
  | 'analise.editais';

export interface ConfiguracaoUsuario {
  tema: 'light' | 'dark' | 'auto';
  notificacoes: {
    email: boolean;
    sistema: boolean;
    tipos: TipoNotificacao[];
  };
  dashboard: {
    banners: string[];
    filtros: Record<string, any>;
  };
}

export type TipoNotificacao = 
  | 'novo_processo'
  | 'prazo_critico'
  | 'recurso_sem_data'
  | 'prioridade_parada'
  | 'gargalo_critico'
  | 'retrabalho';

export interface SessaoUsuario {
  usuario: Usuario;
  token: string;
  expiresAt: Date;
  permissions: Permissao[];
}

// Configurações de perfis padrão
export const PERFIS_PERMISSOES: Record<PerfilUsuario, Permissao[]> = {
  admin: [
    'processos.criar', 'processos.editar', 'processos.visualizar', 'processos.excluir',
    'contratos.criar', 'contratos.editar', 'contratos.visualizar', 'contratos.excluir',
    'usuarios.criar', 'usuarios.editar', 'usuarios.visualizar', 'usuarios.excluir',
    'relatorios.gerar', 'relatorios.exportar',
    'configuracoes.sistema', 'configuracoes.secretaria',
    'prioridades.classificar', 'alertas.gerenciar',
    'pesquisa.precos', 'analise.editais'
  ],
  gestor: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar', 'contratos.editar',
    'usuarios.visualizar',
    'relatorios.gerar', 'relatorios.exportar',
    'configuracoes.secretaria',
    'prioridades.classificar',
    'pesquisa.precos', 'analise.editais'
  ],
  operador: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar',
    'pesquisa.precos', 'analise.editais'
  ],
  consulta: [
    'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar'
  ],
  isabela: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar',
    'pesquisa.precos', 'analise.editais',
    'alertas.gerenciar' // Para receber alertas de recursos sem data
  ],
  tath: [
    'processos.criar', 'processos.editar', 'processos.visualizar',
    'contratos.visualizar',
    'relatorios.gerar',
    'pesquisa.precos', 'analise.editais',
    'alertas.gerenciar' // Para receber alertas de recursos sem data
  ]
};

// Usuários que devem receber alertas específicos
export const USUARIOS_ALERTAS = {
  RECURSO_SEM_DATA: ['isabela', 'tath'],
  PRIORIDADE_GOVERNO: ['admin'], // Secretário e Adjunta de Governo
  GARGALO_CRITICO: ['admin', 'gestor'],
  PRAZO_CRITICO: ['admin', 'gestor', 'operador']
} as const;
