'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Input } from '@/components/ui/input';
import {
  Settings,
  Save,
  RefreshCw,
  Shield,
  Mail,
  Database,
  Globe,
  Palette,
  Bell,
  Lock,
  Server,
  Download,
  Upload,
  Eye,
  EyeOff,
  Check,
  AlertTriangle
} from 'lucide-react';

interface ConfiguracaoItem {
  id: string;
  categoria: 'sistema' | 'seguranca' | 'integracao' | 'notificacao' | 'backup';
  titulo: string;
  descricao: string;
  tipo: 'toggle' | 'input' | 'select' | 'password';
  valor: any;
  opcoes?: string[];
  status: 'ativo' | 'inativo' | 'erro';
}

export default function ConfiguracoesPage() {
  const [configuracoes, setConfiguracoes] = useState<ConfiguracaoItem[]>([
    {
      id: 'tema',
      categoria: 'sistema',
      titulo: 'Tema do Sistema',
      descricao: 'Aparência padrão do sistema (Dark Mode recomendado)',
      tipo: 'select',
      valor: 'dark',
      opcoes: ['light', 'dark', 'auto'],
      status: 'ativo'
    },
    {
      id: 'notificacoes_email',
      categoria: 'notificacao',
      titulo: 'Notificações por Email',
      descricao: 'Enviar alertas automáticos por email',
      tipo: 'toggle',
      valor: true,
      status: 'ativo'
    },
    {
      id: 'smtp_servidor',
      categoria: 'notificacao',
      titulo: 'Servidor SMTP',
      descricao: 'Configuração do servidor de email',
      tipo: 'input',
      valor: 'smtp.maua.sp.gov.br',
      status: 'ativo'
    },
    {
      id: 'smtp_senha',
      categoria: 'notificacao',
      titulo: 'Senha SMTP',
      descricao: 'Senha do servidor de email',
      tipo: 'password',
      valor: '••••••••',
      status: 'ativo'
    },
    {
      id: 'backup_automatico',
      categoria: 'backup',
      titulo: 'Backup Automático',
      descricao: 'Backup diário dos dados do sistema',
      tipo: 'toggle',
      valor: true,
      status: 'ativo'
    },
    {
      id: 'integracao_pncp',
      categoria: 'integracao',
      titulo: 'Integração PNCP',
      descricao: 'Portal Nacional de Contratações Públicas',
      tipo: 'toggle',
      valor: false,
      status: 'inativo'
    },
    {
      id: 'integracao_tce',
      categoria: 'integracao',
      titulo: 'Integração TCE-SP',
      descricao: 'Tribunal de Contas do Estado de São Paulo',
      tipo: 'toggle',
      valor: false,
      status: 'inativo'
    },
    {
      id: 'sessao_timeout',
      categoria: 'seguranca',
      titulo: 'Timeout de Sessão',
      descricao: 'Tempo limite de inatividade (minutos)',
      tipo: 'input',
      valor: '30',
      status: 'ativo'
    },
    {
      id: 'log_auditoria',
      categoria: 'seguranca',
      titulo: 'Log de Auditoria',
      descricao: 'Registrar todas as ações dos usuários',
      tipo: 'toggle',
      valor: true,
      status: 'ativo'
    }
  ]);

  const [senhasVisiveis, setSenhasVisiveis] = useState<{ [key: string]: boolean }>({});
  const [alteracoesPendentes, setAlteracoesPendentes] = useState(false);

  const handleConfigChange = (id: string, novoValor: any) => {
    setConfiguracoes(prev => 
      prev.map(config => 
        config.id === id ? { ...config, valor: novoValor } : config
      )
    );
    setAlteracoesPendentes(true);
  };

  const toggleSenhaVisivel = (id: string) => {
    setSenhasVisiveis(prev => ({
      ...prev,
      [id]: !prev[id]
    }));
  };

  const salvarConfiguracoes = () => {
    // Simular salvamento
    setTimeout(() => {
      setAlteracoesPendentes(false);
      alert('Configurações salvas com sucesso!');
    }, 1000);
  };

  const getCategoriaIcon = (categoria: string) => {
    switch (categoria) {
      case 'sistema': return Palette;
      case 'seguranca': return Shield;
      case 'integracao': return Globe;
      case 'notificacao': return Bell;
      case 'backup': return Database;
      default: return Settings;
    }
  };

  const getCategoriaColor = (categoria: string) => {
    switch (categoria) {
      case 'sistema': return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'seguranca': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'integracao': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'notificacao': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case 'backup': return 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ativo': return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case 'inativo': return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
      case 'erro': return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  const categorias = ['sistema', 'seguranca', 'integracao', 'notificacao', 'backup'];
  
  const estatisticas = {
    total: configuracoes.length,
    ativas: configuracoes.filter(c => c.status === 'ativo').length,
    inativas: configuracoes.filter(c => c.status === 'inativo').length,
    erros: configuracoes.filter(c => c.status === 'erro').length
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between space-y-4 sm:space-y-0">
        <div>
          <h1 className="text-3xl font-bold text-foreground">Configurações do Sistema</h1>
          <p className="text-muted-foreground mt-2">
            Gerencie todas as configurações e integrações do InovaProcess
          </p>
        </div>

        <div className="flex items-center space-x-3">
          {alteracoesPendentes && (
            <Badge variant="outline" className="text-xs bg-yellow-100 text-yellow-800">
              <AlertTriangle className="mr-1 h-3 w-3" />
              Alterações pendentes
            </Badge>
          )}

          <Button variant="outline">
            <RefreshCw className="mr-2 h-4 w-4" />
            Restaurar Padrões
          </Button>

          <Button onClick={salvarConfiguracoes} disabled={!alteracoesPendentes}>
            <Save className="mr-2 h-4 w-4" />
            Salvar Alterações
          </Button>
        </div>
      </div>

      {/* Estatísticas */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total</p>
                <p className="text-2xl font-bold">{estatisticas.total}</p>
              </div>
              <Settings className="h-8 w-8 text-primary" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Ativas</p>
                <p className="text-2xl font-bold text-green-600">{estatisticas.ativas}</p>
              </div>
              <Check className="h-8 w-8 text-green-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Inativas</p>
                <p className="text-2xl font-bold text-gray-600">{estatisticas.inativas}</p>
              </div>
              <Lock className="h-8 w-8 text-gray-600" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Com Erro</p>
                <p className="text-2xl font-bold text-red-600">{estatisticas.erros}</p>
              </div>
              <AlertTriangle className="h-8 w-8 text-red-600" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Configurações por Categoria */}
      {categorias.map(categoria => {
        const configsCategoria = configuracoes.filter(c => c.categoria === categoria);
        if (configsCategoria.length === 0) return null;

        const IconComponent = getCategoriaIcon(categoria);

        return (
          <Card key={categoria}>
            <CardHeader>
              <div className="flex items-center space-x-3">
                <div className="p-2 rounded-lg bg-gray-100 dark:bg-gray-800">
                  <IconComponent className="h-6 w-6 text-primary" />
                </div>
                <div>
                  <CardTitle className="capitalize">{categoria}</CardTitle>
                  <Badge className={getCategoriaColor(categoria)} variant="outline">
                    {configsCategoria.length} configurações
                  </Badge>
                </div>
              </div>
            </CardHeader>

            <CardContent className="space-y-4">
              {configsCategoria.map(config => (
                <div key={config.id} className="flex items-center justify-between p-4 border border-border rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center space-x-3 mb-2">
                      <h4 className="font-medium text-foreground">{config.titulo}</h4>
                      <Badge className={getStatusColor(config.status)}>
                        {config.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">{config.descricao}</p>
                  </div>

                  <div className="ml-4">
                    {config.tipo === 'toggle' && (
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={config.valor}
                          onChange={(e) => handleConfigChange(config.id, e.target.checked)}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 dark:peer-focus:ring-blue-800 rounded-full peer dark:bg-gray-700 peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all dark:border-gray-600 peer-checked:bg-blue-600"></div>
                      </label>
                    )}

                    {config.tipo === 'input' && (
                      <Input
                        type="text"
                        value={config.valor}
                        onChange={(e) => handleConfigChange(config.id, e.target.value)}
                        className="w-48"
                      />
                    )}

                    {config.tipo === 'password' && (
                      <div className="relative">
                        <Input
                          type={senhasVisiveis[config.id] ? 'text' : 'password'}
                          value={config.valor}
                          onChange={(e) => handleConfigChange(config.id, e.target.value)}
                          className="w-48 pr-10"
                        />
                        <button
                          type="button"
                          onClick={() => toggleSenhaVisivel(config.id)}
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        >
                          {senhasVisiveis[config.id] ? (
                            <EyeOff className="h-4 w-4 text-muted-foreground" />
                          ) : (
                            <Eye className="h-4 w-4 text-muted-foreground" />
                          )}
                        </button>
                      </div>
                    )}

                    {config.tipo === 'select' && config.opcoes && (
                      <select
                        value={config.valor}
                        onChange={(e) => handleConfigChange(config.id, e.target.value)}
                        className="px-3 py-2 border border-input rounded-md bg-background text-foreground w-48"
                      >
                        {config.opcoes.map(opcao => (
                          <option key={opcao} value={opcao}>
                            {opcao === 'light' ? 'Claro' : 
                             opcao === 'dark' ? 'Escuro' : 
                             opcao === 'auto' ? 'Automático' : opcao}
                          </option>
                        ))}
                      </select>
                    )}
                  </div>
                </div>
              ))}
            </CardContent>
          </Card>
        );
      })}

      {/* Ações Avançadas */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Server className="h-5 w-5" />
            <span>Ações Avançadas</span>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Download className="h-6 w-6" />
              <span>Exportar Configurações</span>
            </Button>

            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <Upload className="h-6 w-6" />
              <span>Importar Configurações</span>
            </Button>

            <Button variant="outline" className="h-20 flex flex-col items-center justify-center space-y-2">
              <RefreshCw className="h-6 w-6" />
              <span>Reiniciar Sistema</span>
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
