import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { existsSync } from 'fs';
import path from 'path';
import { extrairTRDoEdital, processarEditalComTR } from '@/lib/analise-editais/editalTRExtractor';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const tipo = formData.get('tipo') as string;
    const isAjustado = formData.get('isAjustado') === 'true';
    
    if (!file) {
      return NextResponse.json(
        { success: false, error: 'Nenhum arquivo enviado' },
        { status: 400 }
      );
    }

    if (!['etp', 'edital', 'tr'].includes(tipo)) {
      return NextResponse.json(
        { success: false, error: 'Tipo de documento inválido' },
        { status: 400 }
      );
    }

    // Criar diretório se não existir
    const uploadDir = path.join(process.cwd(), 'data', 'uploads', 'editais');
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true });
    }

    // Gerar nome único para o arquivo
    const timestamp = Date.now();
    const extension = file.name.split('.').pop();
    const fileName = `${tipo}_${isAjustado ? 'ajustado_' : ''}${timestamp}.${extension}`;
    const filePath = path.join(uploadDir, fileName);

    // Salvar arquivo
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filePath, buffer);

    // Simular extração de conteúdo (em produção, usar OCR/PDF parser)
    const conteudoExtraido = await extrairConteudo(buffer, extension || 'pdf');

    // Se for edital, tentar extrair TR automaticamente
    let trExtraido = null;
    let recomendacoes = [];

    if (tipo === 'edital') {
      const resultadoProcessamento = await processarEditalComTR(conteudoExtraido);
      trExtraido = resultadoProcessamento.tr;
      recomendacoes = resultadoProcessamento.recomendacoes;
    }

    return NextResponse.json({
      success: true,
      data: {
        fileName,
        filePath: `/uploads/editais/${fileName}`,
        tipo,
        isAjustado,
        conteudo: conteudoExtraido,
        size: file.size,
        uploadedAt: new Date().toISOString(),
        trExtraido: trExtraido?.encontrado ? trExtraido : null,
        recomendacoes
      }
    });

  } catch (error) {
    console.error('Erro no upload:', error);
    return NextResponse.json(
      { success: false, error: 'Erro interno do servidor' },
      { status: 500 }
    );
  }
}

async function extrairConteudo(buffer: Buffer, extension: string): Promise<string> {
  // Simulação de extração de conteúdo
  // Em produção, usar bibliotecas como pdf-parse, mammoth, etc.
  
  const simulatedContent = {
    pdf: `Conteúdo extraído do PDF...
    
PREÂMBULO
A Prefeitura Municipal de Mauá, através da CLMP, torna público...

OBJETO
Contratação de empresa especializada para...

CONDIÇÕES DE PARTICIPAÇÃO
Poderão participar desta licitação...

DOCUMENTAÇÃO DE HABILITAÇÃO
- Certidão de Regularidade do FGTS
- Certidão Negativa de Débitos Trabalhistas
- Balanço Patrimonial do último exercício

CRITÉRIOS DE JULGAMENTO
Menor preço global...

PRAZOS
- Entrega das propostas: até às 14h00 do dia...
- Início da execução: em até 30 dias...

VALOR ESTIMADO
R$ 150.000,00 (cento e cinquenta mil reais)

CONDIÇÕES DE PAGAMENTO
Pagamento em até 30 dias após...`,

    doc: `Documento Word extraído...
    
ESTUDOS TÉCNICOS PRELIMINARES

1. JUSTIFICATIVA
A presente contratação se justifica pela necessidade...

2. DESCRIÇÃO DO OBJETO
Contratação de serviços de...

3. ANÁLISE DE RISCOS
Os principais riscos identificados são...

4. ESTIMATIVA DE CUSTOS
Baseada em pesquisa de mercado...

5. CRONOGRAMA
Fase 1: Planejamento (30 dias)
Fase 2: Execução (60 dias)
Fase 3: Entrega (15 dias)`,

    default: `Conteúdo do documento extraído automaticamente...
    
Este é um documento de licitação que contém informações sobre:
- Objeto da contratação
- Condições de participação
- Documentação necessária
- Prazos e cronogramas
- Valores estimados
- Critérios de julgamento`
  };

  return simulatedContent[extension as keyof typeof simulatedContent] || simulatedContent.default;
}
