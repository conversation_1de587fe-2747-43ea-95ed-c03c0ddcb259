import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Per<PERSON>lUsuario, PERFIS_PERMISSOES } from '@/types/usuario';

// Mock de usuários para desenvolvimento
const USUARIOS_MOCK: Usuario[] = [
  {
    id: '1',
    nome: '<PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 99999-9999',
    secretaria: 'CLMP',
    cargo: 'Coordenador',
    perfil: 'admin',
    status: 'ativo',
    ultimoAcesso: '2025-06-15 14:30',
    dataCriacao: '2024-01-15',
    permissoes: PERFIS_PERMISSOES.admin
  },
  {
    id: '2',
    nome: '<PERSON><PERSON>',
    email: '<EMAIL>',
    telefone: '(11) 98888-8888',
    secretaria: 'CLMP',
    cargo: 'Analista de Processos',
    perfil: 'isabela',
    status: 'ativo',
    ultimoAcesso: '2025-06-15 13:45',
    dataCriacao: '2024-02-20',
    permissoes: PERFIS_PERMISSOES.isabela
  },
  {
    id: '3',
    nome: 'Tath Silva',
    email: '<EMAIL>',
    telefone: '(11) 97777-7777',
    secretaria: 'CLMP',
    cargo: 'Analista de Processos',
    perfil: 'tath',
    status: 'ativo',
    ultimoAcesso: '2025-06-15 12:30',
    dataCriacao: '2024-03-10',
    permissoes: PERFIS_PERMISSOES.tath
  },
  {
    id: '4',
    nome: 'Ana Gestora',
    email: '<EMAIL>',
    telefone: '(11) 96666-6666',
    secretaria: 'SE',
    cargo: 'Secretária Adjunta',
    perfil: 'gestor',
    status: 'ativo',
    ultimoAcesso: '2025-06-15 11:15',
    dataCriacao: '2024-01-20',
    permissoes: PERFIS_PERMISSOES.gestor
  },
  {
    id: '5',
    nome: 'João Operador',
    email: '<EMAIL>',
    telefone: '(11) 95555-5555',
    secretaria: 'SMS',
    cargo: 'Analista',
    perfil: 'operador',
    status: 'ativo',
    ultimoAcesso: '2025-06-15 10:00',
    dataCriacao: '2024-04-15',
    permissoes: PERFIS_PERMISSOES.operador
  }
];

// Sessão atual (simulação - em produção seria gerenciada por JWT/cookies)
let sessaoAtual: SessaoUsuario | null = null;

/**
 * Autentica usuário com email (Google OAuth ou desenvolvimento)
 */
export async function login(email: string, senha: string): Promise<SessaoUsuario | null> {
  // Simulação de autenticação - em produção seria validado no backend
  const usuario = USUARIOS_MOCK.find(u => u.email === email && u.status === 'ativo');

  if (!usuario) {
    throw new Error('Usuário não encontrado ou não autorizado no sistema');
  }

  // Para Google OAuth, aceitar qualquer senha (em produção seria validado pelo Google)
  if (senha !== 'google_oauth' && senha !== 'maua2025') {
    throw new Error('Credenciais inválidas');
  }

  // Criar sessão
  const sessao: SessaoUsuario = {
    usuario,
    token: generateToken(),
    expiresAt: new Date(Date.now() + 8 * 60 * 60 * 1000), // 8 horas
    permissions: usuario.permissoes
  };

  // Atualizar último acesso
  usuario.ultimoAcesso = new Date().toISOString();
  
  sessaoAtual = sessao;
  
  // Salvar no localStorage (em produção seria httpOnly cookie)
  if (typeof window !== 'undefined') {
    localStorage.setItem('inovaprocess_session', JSON.stringify(sessao));
  }

  return sessao;
}

/**
 * Logout do usuário
 */
export function logout(): void {
  sessaoAtual = null;
  if (typeof window !== 'undefined') {
    localStorage.removeItem('inovaprocess_session');
  }
}

/**
 * Obtém a sessão atual
 */
export function getSessaoAtual(): SessaoUsuario | null {
  if (sessaoAtual) {
    return sessaoAtual;
  }

  // Tentar recuperar do localStorage
  if (typeof window !== 'undefined') {
    const sessionData = localStorage.getItem('inovaprocess_session');
    if (sessionData) {
      try {
        const sessao = JSON.parse(sessionData) as SessaoUsuario;
        
        // Verificar se não expirou
        if (new Date(sessao.expiresAt) > new Date()) {
          sessaoAtual = sessao;
          return sessao;
        } else {
          // Sessão expirada
          localStorage.removeItem('inovaprocess_session');
        }
      } catch (error) {
        console.error('Erro ao recuperar sessão:', error);
        localStorage.removeItem('inovaprocess_session');
      }
    }
  }

  return null;
}

/**
 * Verifica se usuário está autenticado
 */
export function isAuthenticated(): boolean {
  const sessao = getSessaoAtual();
  return sessao !== null && new Date(sessao.expiresAt) > new Date();
}

/**
 * Verifica se usuário tem permissão específica
 */
export function hasPermission(permissao: string): boolean {
  const sessao = getSessaoAtual();
  if (!sessao) return false;
  
  return sessao.permissions.includes(permissao as any);
}

/**
 * Verifica se usuário pode acessar recurso
 */
export function canAccess(recurso: string, acao: string): boolean {
  return hasPermission(`${recurso}.${acao}`);
}

/**
 * Obtém usuário atual
 */
export function getUsuarioAtual(): Usuario | null {
  const sessao = getSessaoAtual();
  return sessao?.usuario || null;
}

/**
 * Verifica se usuário é admin
 */
export function isAdmin(): boolean {
  const usuario = getUsuarioAtual();
  return usuario?.perfil === 'admin';
}

/**
 * Verifica se usuário é Isabela ou Tath (para alertas específicos)
 */
export function isUsuarioCadastro(): boolean {
  const usuario = getUsuarioAtual();
  return usuario?.perfil === 'isabela' || usuario?.perfil === 'tath';
}

/**
 * Gera token simples (em produção seria JWT)
 */
function generateToken(): string {
  return Math.random().toString(36).substring(2) + Date.now().toString(36);
}

/**
 * Registra novo usuário (apenas admins)
 */
export async function registrarUsuario(dadosUsuario: Omit<Usuario, 'id' | 'dataCriacao' | 'ultimoAcesso' | 'permissoes'>): Promise<Usuario> {
  if (!isAdmin()) {
    throw new Error('Apenas administradores podem registrar usuários');
  }

  const novoUsuario: Usuario = {
    ...dadosUsuario,
    id: (USUARIOS_MOCK.length + 1).toString(),
    dataCriacao: new Date().toISOString(),
    permissoes: PERFIS_PERMISSOES[dadosUsuario.perfil]
  };

  USUARIOS_MOCK.push(novoUsuario);
  return novoUsuario;
}

/**
 * Lista todos os usuários (apenas admins)
 */
export function listarUsuarios(): Usuario[] {
  if (!isAdmin()) {
    throw new Error('Apenas administradores podem listar usuários');
  }
  
  return USUARIOS_MOCK;
}

/**
 * Atualiza usuário (apenas admins ou próprio usuário)
 */
export async function atualizarUsuario(id: string, dados: Partial<Usuario>): Promise<Usuario> {
  const usuarioAtual = getUsuarioAtual();
  if (!usuarioAtual) {
    throw new Error('Usuário não autenticado');
  }

  // Verificar permissões
  if (!isAdmin() && usuarioAtual.id !== id) {
    throw new Error('Sem permissão para atualizar este usuário');
  }

  const index = USUARIOS_MOCK.findIndex(u => u.id === id);
  if (index === -1) {
    throw new Error('Usuário não encontrado');
  }

  // Não permitir mudança de perfil por usuários não-admin
  if (!isAdmin() && dados.perfil && dados.perfil !== USUARIOS_MOCK[index].perfil) {
    throw new Error('Apenas administradores podem alterar perfis');
  }

  USUARIOS_MOCK[index] = { ...USUARIOS_MOCK[index], ...dados };
  
  // Atualizar permissões se perfil mudou
  if (dados.perfil) {
    USUARIOS_MOCK[index].permissoes = PERFIS_PERMISSOES[dados.perfil];
  }

  return USUARIOS_MOCK[index];
}
