'use client';

import { useState, useEffect } from 'react';
import Link from 'next/link';
import { useParams } from 'next/navigation';
import { Processo } from '@/types/processo';
import {
  ArrowLeft,
  Calendar,
  Building,
  User,
  FileText,
  DollarSign,
  AlertCircle,
  Loader2
} from 'lucide-react';

interface ProcessoResponse {
  success: boolean;
  data: Processo;
  error?: string;
}

export default function ProcessoDetalhesPage() {
  const params = useParams();
  const [processo, setProcesso] = useState<Processo | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const loadProcesso = async () => {
      // Aguardar params se necessário (Next.js 15)
      const resolvedParams = await Promise.resolve(params);
      if (!resolvedParams.id) return;

      setLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/processos/${encodeURIComponent(resolvedParams.id as string)}`);
        const data: ProcessoResponse = await response.json();

        if (data.success) {
          setProcesso(data.data);
        } else {
          setError(data.error || 'Processo não encontrado');
        }
      } catch (err) {
        setError('Erro de conexão. Tente novamente.');
        console.error('Erro ao carregar processo:', err);
      } finally {
        setLoading(false);
      }
    };

    loadProcesso();
  }, [params]);

  // Função para determinar a cor do status
  const getStatusColor = (status: string) => {
    const statusLower = status.toLowerCase();

    if (statusLower.includes('concluído') || statusLower.includes('finalizado')) {
      return 'bg-green-100 text-green-800 border-green-200';
    }
    if (statusLower.includes('andamento') || statusLower.includes('aberta')) {
      return 'bg-blue-100 text-blue-800 border-blue-200';
    }
    if (statusLower.includes('cancelado') || statusLower.includes('suspenso')) {
      return 'bg-red-100 text-red-800 border-red-200';
    }
    if (statusLower.includes('aguardando') || statusLower.includes('análise')) {
      return 'bg-yellow-100 text-yellow-800 border-yellow-200';
    }

    return 'bg-gray-100 text-gray-800 border-gray-200';
  };

  // Função para formatar valor monetário
  const formatCurrency = (value: string) => {
    if (!value || value === '-') return 'Não informado';
    return value;
  };

  // Função para formatar data
  const formatDate = (dateString: string) => {
    if (!dateString || dateString === '-') return 'Não informado';
    return dateString;
  };

  // Função para calcular tempo no setor atual
  const calcularTempoNoSetor = (processo: Processo) => {
    if (!processo.DATA) return null;

    try {
      // Converter data do formato DD/MM/YYYY para Date
      const [dia, mes, ano] = processo.DATA.split('/');
      const dataProcesso = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
      const agora = new Date();

      // Calcular diferença em milissegundos
      const diffMs = agora.getTime() - dataProcesso.getTime();
      const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
      const diffHoras = Math.floor((diffMs % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));

      if (diffDias > 0) {
        return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
      } else if (diffHoras > 0) {
        return `${diffHoras} hora${diffHoras > 1 ? 's' : ''}`;
      } else {
        return 'Menos de 1 hora';
      }
    } catch (error) {
      return null;
    }
  };

  // Função para calcular dias entre duas datas
  const calcularDiasEntreDatas = (dataInicio: string, dataFim: string) => {
    try {
      const [diaI, mesI, anoI] = dataInicio.split('/');
      const [diaF, mesF, anoF] = dataFim.split('/');
      const inicio = new Date(parseInt(anoI), parseInt(mesI) - 1, parseInt(diaI));
      const fim = new Date(parseInt(anoF), parseInt(mesF) - 1, parseInt(diaF));
      const diff = Math.floor((fim.getTime() - inicio.getTime()) / (1000 * 60 * 60 * 24));
      return diff;
    } catch {
      return 0;
    }
  };

  // Função para criar cronologia detalhada
  const criarCronologiaDetalhada = (processo: Processo) => {
    const eventos: Array<{
      data: string;
      evento: string;
      local: string;
      cor: string;
      diasProximo?: number;
    }> = [];

    if (processo['DATA DE INÍCIO DO PROCESSO']) {
      eventos.push({
        data: processo['DATA DE INÍCIO DO PROCESSO'],
        evento: 'Início do Processo',
        local: 'Secretaria',
        cor: 'bg-blue-500'
      });
    }

    if (processo['DATA ENTRADA NA CLMP']) {
      eventos.push({
        data: processo['DATA ENTRADA NA CLMP'],
        evento: 'Entrada na CLMP',
        local: 'CLMP',
        cor: 'bg-green-500'
      });
    }

    if (processo['DATA PUBLICAÇÃO']) {
      eventos.push({
        data: processo['DATA PUBLICAÇÃO'],
        evento: 'Publicação do Edital',
        local: 'CLMP',
        cor: 'bg-purple-500'
      });
    }

    if (processo['DATA ABERTURA']) {
      eventos.push({
        data: processo['DATA ABERTURA'],
        evento: 'Abertura da Licitação',
        local: 'CLMP',
        cor: 'bg-orange-500'
      });
    }

    if (processo.DATA) {
      eventos.push({
        data: processo.DATA,
        evento: `Status Atual: ${processo.STATUS}`,
        local: processo.LOCAL || 'CLMP',
        cor: 'bg-yellow-500'
      });
    }

    // Calcular dias entre eventos
    for (let i = 0; i < eventos.length - 1; i++) {
      const dias = calcularDiasEntreDatas(eventos[i].data, eventos[i + 1].data);
      eventos[i].diasProximo = dias;
    }

    return eventos;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="text-center">
          <Loader2 className="mx-auto h-8 w-8 animate-spin text-primary mb-4" />
          <p className="text-muted-foreground">Carregando detalhes do processo...</p>
        </div>
      </div>
    );
  }

  if (error || !processo) {
    return (
      <div className="space-y-6">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>

        <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-6 text-center">
          <AlertCircle className="mx-auto h-12 w-12 text-destructive mb-4" />
          <h3 className="text-lg font-medium text-destructive mb-2">
            Erro ao carregar processo
          </h3>
          <p className="text-destructive/80 mb-4">{error}</p>
          <Link
            href="/processos"
            className="bg-destructive text-destructive-foreground px-4 py-2 rounded-md hover:bg-destructive/90 transition-colors"
          >
            Voltar para lista
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-4">
          <Link
            href="/processos"
            className="flex items-center text-muted-foreground hover:text-foreground transition-colors"
          >
            <ArrowLeft size={20} className="mr-2" />
            Voltar para processos
          </Link>
        </div>
      </div>

      {/* Título e Status */}
      <div className="bg-background rounded-lg shadow-sm border p-6">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between mb-4">
          <div>
            <h1 className="text-2xl font-bold text-foreground mb-2">
              {processo.PROCESSO || 'Processo sem número'}
            </h1>
            <p className="text-muted-foreground">
              Item: {processo.ITEM || 'Não informado'}
            </p>
          </div>

          {processo.STATUS && (
            <div className="mt-4 sm:mt-0">
              <span className={`px-4 py-2 rounded-full text-sm font-medium border ${getStatusColor(processo.STATUS)}`}>
                {processo.STATUS}
              </span>
            </div>
          )}
        </div>

        {/* Tempo no setor atual */}
        {calcularTempoNoSetor(processo) && (
          <div className="border-t pt-4 mb-4">
            <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <Calendar className="mr-3 text-yellow-600 dark:text-yellow-400" size={20} />
                  <div>
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">Tempo no Setor Atual</h3>
                    <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                      {calcularTempoNoSetor(processo)}
                    </p>
                    <p className="text-sm text-yellow-700 dark:text-yellow-300">
                      Local: {processo.LOCAL || 'Não informado'} | Responsável: {processo.RESPONSÁVEL || 'Não informado'}
                    </p>
                  </div>
                </div>
                {processo['DATA DE INÍCIO DO PROCESSO'] && (
                  <div className="text-right">
                    <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200 mb-1">Processo Aberto Há</h3>
                    <p className="text-lg font-bold text-yellow-900 dark:text-yellow-100">
                      {(() => {
                        try {
                          const [dia, mes, ano] = processo['DATA DE INÍCIO DO PROCESSO'].split('/');
                          const dataInicio = new Date(parseInt(ano), parseInt(mes) - 1, parseInt(dia));
                          const agora = new Date();
                          const diffMs = agora.getTime() - dataInicio.getTime();
                          const diffDias = Math.floor(diffMs / (1000 * 60 * 60 * 24));
                          return `${diffDias} dia${diffDias > 1 ? 's' : ''}`;
                        } catch {
                          return 'N/A';
                        }
                      })()}
                    </p>
                  </div>
                )}
              </div>
            </div>
          </div>
        )}

        {/* Objeto - com scroll interno */}
        <div className="border-t pt-4">
          <h3 className="text-sm font-medium text-muted-foreground mb-2">OBJETO</h3>
          <div className="max-h-20 overflow-y-auto bg-muted/30 rounded p-3">
            <p className="text-foreground leading-relaxed">
              {processo.OBJETO || 'Objeto não informado'}
            </p>
          </div>
        </div>
      </div>

      {/* Informações Principais */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Informações Gerais */}
        <div className="bg-background rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <FileText className="mr-2" size={20} />
            Informações Gerais
          </h2>

          <div className="space-y-4 max-h-96 overflow-y-auto">
            <div className="flex items-center">
              <Building className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Secretaria Requisitante</span>
                <p className="font-medium text-foreground">{processo.REQUISITANTE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <FileText className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Modalidade</span>
                <p className="font-medium text-foreground">{processo.MODALIDADE || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <User className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Responsável Atual</span>
                <p className="font-medium text-foreground">{processo.RESPONSÁVEL || 'Não informado'}</p>
              </div>
            </div>

            <div className="flex items-center">
              <Building className="mr-3 text-muted-foreground" size={16} />
              <div>
                <span className="text-sm text-muted-foreground">Local Atual</span>
                <p className="font-medium text-foreground">{processo.LOCAL || 'Não informado'}</p>
              </div>
            </div>

            {processo.PRIORIDADE && (
              <div className="flex items-center">
                <div className="mr-3 text-muted-foreground">⚡</div>
                <div>
                  <span className="text-sm text-muted-foreground">Prioridade</span>
                  <p className="font-medium text-foreground">{processo.PRIORIDADE}</p>
                </div>
              </div>
            )}

            {processo['Nº DO CERTAME'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Número do Certame</span>
                  <p className="font-medium text-foreground">{processo['Nº DO CERTAME']}</p>
                </div>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Número do Contrato</span>
                  <p className="font-medium text-foreground">{processo['CONTRATO NÚMERO']}</p>
                </div>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div className="flex items-center">
                <Calendar className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Vencimento</span>
                  <p className="font-medium text-foreground">{formatDate(processo.VENCIMENTO)}</p>
                </div>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div className="flex items-center">
                <FileText className="mr-3 text-muted-foreground" size={16} />
                <div>
                  <span className="text-sm text-muted-foreground">Processo de Gerenciamento</span>
                  <p className="font-medium text-foreground">{processo['PROCESSO DE GERENCIAMENTO']}</p>
                </div>
              </div>
            )}
          </div>
        </div>

        {/* Cronologia Completa */}
        <div className="bg-background rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
            <Calendar className="mr-2" size={20} />
            Cronologia e Tramitação
          </h2>

          <div className="space-y-4">
            {/* Timeline visual com scroll */}
            <div className="max-h-80 overflow-y-auto">
              <div className="relative">
                <div className="absolute left-4 top-0 bottom-0 w-0.5 bg-border"></div>

                {criarCronologiaDetalhada(processo).map((evento, index) => (
                  <div key={index} className="relative flex items-start pb-6 last:pb-0">
                    <div className={`absolute left-2 w-4 h-4 ${evento.cor} rounded-full border-2 border-background shadow z-10`}></div>
                    <div className="ml-10 flex-1">
                      <div className="flex items-center justify-between">
                        <span className="text-sm font-medium text-foreground">{evento.evento}</span>
                        <span className="text-xs text-muted-foreground bg-muted px-2 py-1 rounded">
                          {evento.local}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground mt-1">{formatDate(evento.data)}</p>
                      {evento.diasProximo !== undefined && evento.diasProximo > 0 && (
                        <div className="mt-2 text-xs bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 px-2 py-1 rounded inline-block">
                          ⏱️ {evento.diasProximo} dia{evento.diasProximo > 1 ? 's' : ''} até próximo evento
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Resumo de tempos - Totais no rodapé */}
            <div className="mt-6 pt-4 border-t border-border">
              <h3 className="text-sm font-medium text-foreground mb-3">Resumo de Tempos</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3">
                {processo['DATA DE INÍCIO DO PROCESSO'] && processo['DATA ENTRADA NA CLMP'] && (
                  <div className="bg-red-50 dark:bg-red-900/20 p-3 rounded-lg border border-red-200 dark:border-red-800">
                    <span className="text-xs text-red-600 dark:text-red-400 font-medium">Tempo Fora da CLMP</span>
                    <p className="font-bold text-sm text-red-800 dark:text-red-200">
                      {(() => {
                        const dias = calcularDiasEntreDatas(processo['DATA DE INÍCIO DO PROCESSO'], processo['DATA ENTRADA NA CLMP']);
                        return `${dias} dia${dias > 1 ? 's' : ''}`;
                      })()}
                    </p>
                    <p className="text-xs text-red-600 dark:text-red-400 mt-1">Na Secretaria</p>
                  </div>
                )}

                {processo['DATA ENTRADA NA CLMP'] && processo.DATA && (
                  <div className="bg-green-50 dark:bg-green-900/20 p-3 rounded-lg border border-green-200 dark:border-green-800">
                    <span className="text-xs text-green-600 dark:text-green-400 font-medium">Tempo na CLMP</span>
                    <p className="font-bold text-sm text-green-800 dark:text-green-200">
                      {(() => {
                        const dias = calcularDiasEntreDatas(processo['DATA ENTRADA NA CLMP'], processo.DATA);
                        return `${dias} dia${dias > 1 ? 's' : ''}`;
                      })()}
                    </p>
                    <p className="text-xs text-green-600 dark:text-green-400 mt-1">Coordenadoria de Licitações</p>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Valores e Fontes */}
      <div className="bg-background rounded-lg shadow-sm border p-6">
        <h2 className="text-lg font-semibold text-foreground mb-4 flex items-center">
          <DollarSign className="mr-2" size={20} />
          Informações Financeiras
        </h2>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {processo['VALOR ESTIMADO'] && processo['VALOR ESTIMADO'] !== '-' && (
            <div className="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg border border-blue-200 dark:border-blue-800">
              <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">Valor Estimado</span>
              <p className="text-lg font-bold text-blue-900 dark:text-blue-100">{formatCurrency(processo['VALOR ESTIMADO'])}</p>
            </div>
          )}

          {processo['VALOR CONTRATADO'] && processo['VALOR CONTRATADO'] !== '-' && (
            <div className="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg border border-green-200 dark:border-green-800">
              <span className="text-sm text-green-600 dark:text-green-400 font-medium">Valor Contratado</span>
              <p className="text-lg font-bold text-green-900 dark:text-green-100">{formatCurrency(processo['VALOR CONTRATADO'])}</p>
            </div>
          )}

          {/* Fontes de Recursos */}
          <div className="md:col-span-2 lg:col-span-1">
            <h3 className="text-sm font-medium text-foreground mb-3">Fontes de Recursos</h3>
            <div className="space-y-2 max-h-32 overflow-y-auto">
              {processo['Fonte 0001 (TESOURO)'] && processo['Fonte 0001 (TESOURO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Tesouro:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0001 (TESOURO)'])}</span>
                </div>
              )}
              {processo['Fonte 0002 (ESTADUAL)'] && processo['Fonte 0002 (ESTADUAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Estadual:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0002 (ESTADUAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0003 (FUNDO)'] && processo['Fonte 0003 (FUNDO)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Fundo:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0003 (FUNDO)'])}</span>
                </div>
              )}
              {processo['Fonte 0005 (FEDERAL)'] && processo['Fonte 0005 (FEDERAL)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-muted-foreground">Federal:</span>
                  <span className="ml-2 font-medium text-foreground">{formatCurrency(processo['Fonte 0005 (FEDERAL)'])}</span>
                </div>
              )}
              {processo['Fonte 0007 (FINISA)'] && processo['Fonte 0007 (FINISA)'] !== '-' && (
                <div className="text-sm">
                  <span className="text-gray-500">FINISA:</span>
                  <span className="ml-2 font-medium">{formatCurrency(processo['Fonte 0007 (FINISA)'])}</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Informações do Certame e Contrato */}
      {(processo['Nº DO CERTAME'] || processo['CONTRATO NÚMERO']) && (
        <div className="bg-white rounded-lg shadow-sm border p-6">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">
            Certame e Contrato
          </h2>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {processo['Nº DO CERTAME'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Certame</span>
                <p className="font-medium text-lg">{processo['Nº DO CERTAME']}</p>
              </div>
            )}

            {processo['CONTRATO NÚMERO'] && (
              <div>
                <span className="text-sm text-gray-500">Número do Contrato</span>
                <p className="font-medium text-lg">{processo['CONTRATO NÚMERO']}</p>
              </div>
            )}

            {processo.VENCIMENTO && processo.VENCIMENTO !== '-' && (
              <div>
                <span className="text-sm text-gray-500">Vencimento</span>
                <p className="font-medium">{formatDate(processo.VENCIMENTO)}</p>
              </div>
            )}

            {processo['PROCESSO DE GERENCIAMENTO'] && (
              <div>
                <span className="text-sm text-gray-500">Processo de Gerenciamento</span>
                <p className="font-medium">{processo['PROCESSO DE GERENCIAMENTO']}</p>
              </div>
            )}
          </div>
        </div>
      )}
    </div>
  );
}

