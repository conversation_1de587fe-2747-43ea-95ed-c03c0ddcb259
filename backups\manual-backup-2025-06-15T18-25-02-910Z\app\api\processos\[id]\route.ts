import { NextRequest, NextResponse } from 'next/server';
import { CSVReader } from '@/lib/csvReader';

export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;

    // Log para debug
    console.log('API chamada para processo ID:', id);

    // Decodificar o ID (pode conter caracteres especiais)
    const numeroProcesso = decodeURIComponent(id);
    console.log('Número do processo decodificado:', numeroProcesso);

    // Buscar o processo específico
    const processo = await CSVReader.getProcessoById(numeroProcesso);
    console.log('Processo encontrado:', processo ? 'SIM' : 'NÃO');

    if (!processo) {
      return NextResponse.json(
        {
          success: false,
          error: 'Processo não encontrado',
          debug: { id, numeroProcesso }
        },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data: processo,
    });
  } catch (error) {
    console.error('Erro na API de processo específico:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}

export async function PATCH(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params;
    const body = await request.json();

    console.log('PATCH processo ID:', id, 'Body:', body);

    // Por enquanto, apenas simular sucesso
    // TODO: Implementar atualização real no CSV ou banco de dados

    return NextResponse.json({
      success: true,
      message: 'Processo atualizado com sucesso',
      data: {
        id,
        ...body
      }
    });
  } catch (error) {
    console.error('Erro ao atualizar processo:', error);
    return NextResponse.json(
      {
        success: false,
        error: 'Erro interno do servidor',
        debug: error instanceof Error ? error.message : 'Erro desconhecido'
      },
      { status: 500 }
    );
  }
}
