'use client';

import { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { login } from '@/lib/auth';
import {
  LogIn,
  Mail,
  Lock,
  Building,
  Shield,
  AlertCircle,
  CheckCircle,
  Eye,
  EyeOff
} from 'lucide-react';

export default function LoginPage() {
  const router = useRouter();
  const [formData, setFormData] = useState({
    email: '',
    senha: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
    if (error) setError(''); // Limpar erro ao digitar
  };

  const handleGoogleLogin = async () => {
    setLoading(true);
    setError('');

    try {
      // Simular login com Google OAuth
      // Em produção seria: window.location.href = '/api/auth/google'
      console.log('🔐 Iniciando login com Google...');

      // Simular usuário autenticado via Google
      const mockGoogleUser = {
        email: '<EMAIL>',
        name: 'Marcos Isidoro',
        picture: 'https://via.placeholder.com/150'
      };

      // Verificar se usuário existe no sistema
      const sessao = await login(mockGoogleUser.email, 'google_oauth');
      if (sessao) {
        router.push('/dashboard');
      }

    } catch (err) {
      setError('Usuário não autorizado no sistema');
      setLoading(false);
    }
  };

  const isFormValid = formData.email && formData.senha;

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-gray-900 dark:to-gray-800 flex items-center justify-center p-4">
      <div className="w-full max-w-md space-y-6">
        {/* Header */}
        <div className="text-center space-y-2">
          <div className="flex items-center justify-center space-x-2">
            <Building className="h-8 w-8 text-primary" />
            <h1 className="text-2xl font-bold text-foreground">InovaProcess</h1>
          </div>
          <p className="text-muted-foreground">
            Sistema de Gestão de Processos
          </p>

        </div>

        {/* Login com Google */}
        <Card className="shadow-lg">
          <CardHeader className="space-y-1">
            <CardTitle className="text-xl text-center flex items-center justify-center">
              <LogIn className="mr-2 h-5 w-5" />
              Entrar no Sistema
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {error && (
                <Alert variant="destructive">
                  <AlertCircle className="h-4 w-4" />
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}

              <Button
                onClick={handleGoogleLogin}
                className="w-full bg-white hover:bg-gray-50 text-gray-900 border border-gray-300"
                disabled={loading}
              >
                {loading ? (
                  <>
                    <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-gray-900 border-t-transparent" />
                    Conectando...
                  </>
                ) : (
                  <>
                    <svg className="mr-2 h-4 w-4" viewBox="0 0 24 24">
                      <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"/>
                      <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"/>
                      <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"/>
                      <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"/>
                    </svg>
                    Entrar com Google
                  </>
                )}
              </Button>
            </div>
          </CardContent>
        </Card>

        {/* Informações de Acesso */}
        <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-blue-600" />
                <span className="text-sm font-medium text-blue-800 dark:text-blue-200">
                  Acesso Seguro
                </span>
              </div>
              
              <div className="space-y-2 text-xs text-blue-700 dark:text-blue-300">
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  <span>Use sua conta Google institucional</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  <span>Autenticação segura via Google OAuth</span>
                </div>
                <div className="flex items-start space-x-2">
                  <CheckCircle className="h-3 w-3 mt-0.5 flex-shrink-0" />
                  <span>Primeiro acesso? Entre em contato com o administrador</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Usuários de Teste */}
        <Card className="bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800">
          <CardContent className="pt-6">
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600" />
                <span className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  Usuários de Teste
                </span>
              </div>
              
              <div className="space-y-2 text-xs text-yellow-700 dark:text-yellow-300">
                <div><strong>Admin:</strong> Clique no botão Google para simular</div>
                <div><strong>Desenvolvimento:</strong> Login automático habilitado</div>
                <div><strong>Produção:</strong> Será integrado com Google Workspace</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Footer */}
        <div className="text-center text-xs text-muted-foreground">
          <p>InovaProcess © 2025</p>
        </div>
      </div>
    </div>
  );
}
