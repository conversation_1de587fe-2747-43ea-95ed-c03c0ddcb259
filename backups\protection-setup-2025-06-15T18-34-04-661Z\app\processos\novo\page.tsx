'use client';

import { useState } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import CurrencyInput from '@/components/ui/currency-input';
import {
  Plus,
  Save,
  ArrowLeft,
  Calendar,
  Building,
  FileText,
  AlertCircle,
  CheckCircle,
  DollarSign,
  Loader2,
  AlertTriangle
} from 'lucide-react';
import Link from 'next/link';

export default function NovoProcessoPage() {
  const [saving, setSaving] = useState(false);
  const [formData, setFormData] = useState({
    numeroProcesso: '',
    objeto: '',
    modalidade: '',
    secretaria: '',
    responsavel: '',
    valorEstimado: '',
    dataInicio: '',
    dataEntradaCLMP: '',
    prioridade: 'Normal',
    status: 'Para Pesquisa de Preços',
    local: 'CLMP',
    observacoes: '',
    // Fontes de recursos
    fonteTesouro: '',
    fonteEstadual: '',
    fonteFundo: '',
    fonteFederal: '',
    fonteFinisa: '',
    // Datas de vencimento das fontes não-tesouro
    dataVencimentoEstadual: '',
    dataVencimentoFundo: '',
    dataVencimentoFederal: '',
    dataVencimentoFinisa: ''
  });

  // Palavras-chave que indicam prioridade alta
  const palavrasChavePrioridadeAlta = [
    'medicamento', 'medicamentos', 'farmácia', 'farmacêutico',
    'judicial', 'liminar', 'mandado', 'decisão judicial',
    'urgente', 'emergência', 'emergencial',
    'saúde pública', 'epidemia', 'pandemia',
    'segurança pública', 'bombeiros', 'defesa civil',
    'merenda escolar', 'alimentação escolar',
    'combustível', 'energia elétrica', 'água',
    'convênio', 'federal', 'estadual', 'fundo'
  ];

  const detectarPrioridadeAlta = (objeto: string): boolean => {
    const objetoLower = objeto.toLowerCase();
    return palavrasChavePrioridadeAlta.some(palavra =>
      objetoLower.includes(palavra.toLowerCase())
    );
  };

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => {
      const newData = {
        ...prev,
        [field]: value
      };

      // Se o campo é objeto, verificar se deve definir prioridade alta
      if (field === 'objeto' && value) {
        const temPrioridadeAlta = detectarPrioridadeAlta(value);
        if (temPrioridadeAlta) {
          newData.prioridade = 'Alta';
        }
      }

      return newData;
    });
  };

  const handleSave = async () => {
    setSaving(true);

    try {
      // Criar objeto do processo com todos os campos
      const novoProcesso = {
        PROCESSO: formData.numeroProcesso,
        OBJETO: formData.objeto,
        MODALIDADE: formData.modalidade,
        REQUISITANTE: formData.secretaria,
        RESPONSÁVEL: formData.responsavel,
        'VALOR ESTIMADO': formData.valorEstimado,
        'DATA DE INÍCIO DO PROCESSO': formData.dataInicio,
        'DATA ENTRADA NA CLMP': formData.dataEntradaCLMP,
        PRIORIDADE: formData.prioridade,
        STATUS: formData.status,
        LOCAL: formData.local,
        // Fontes de recursos
        'Fonte 0001 (TESOURO)': formData.fonteTesouro || 'R$ 0,00',
        'Fonte 0002 (ESTADUAL)': formData.fonteEstadual || 'R$ 0,00',
        'Fonte 0003 (FUNDO)': formData.fonteFundo || 'R$ 0,00',
        'Fonte 0005 (FEDERAL)': formData.fonteFederal || 'R$ 0,00',
        'Fonte 0007 (FINISA)': formData.fonteFinisa || 'R$ 0,00',
        // Datas de vencimento das fontes não-tesouro
        'Data Vencimento Estadual': formData.dataVencimentoEstadual || '',
        'Data Vencimento Fundo': formData.dataVencimentoFundo || '',
        'Data Vencimento Federal': formData.dataVencimentoFederal || '',
        'Data Vencimento Finisa': formData.dataVencimentoFinisa || '',
        // Observações
        OBSERVACOES: formData.observacoes
      };

      // Verificar se precisa gerar alerta para recursos sem data
      const fontesComProblema = [];
      if (formData.fonteEstadual && !formData.dataVencimentoEstadual) fontesComProblema.push('Estadual');
      if (formData.fonteFundo && !formData.dataVencimentoFundo) fontesComProblema.push('Fundo');
      if (formData.fonteFederal && !formData.dataVencimentoFederal) fontesComProblema.push('Federal');
      if (formData.fonteFinisa && !formData.dataVencimentoFinisa) fontesComProblema.push('Finisa');

      if (fontesComProblema.length > 0) {
        console.log(`⚠️ ALERTA: Processo ${formData.numeroProcesso} tem recursos externos sem datas de vencimento:`, fontesComProblema);
        // Aqui seria enviado alerta para Isabela e Tath
      }

      // Simular salvamento
      console.log('💾 Salvando processo:', novoProcesso);

      setTimeout(() => {
        setSaving(false);
        alert(`✅ Processo ${formData.numeroProcesso} salvo com sucesso!${fontesComProblema.length > 0 ? '\n\n⚠️ Alerta gerado para Isabela e Tath: Recursos externos sem datas de vencimento.' : ''}`);
        // Aqui redirecionaria para a lista de processos
      }, 2000);

    } catch (error) {
      setSaving(false);
      alert('❌ Erro ao salvar processo. Tente novamente.');
    }
  };

  const isFormValid = formData.numeroProcesso && formData.objeto && formData.modalidade && formData.secretaria;

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between">
        <div className="flex items-center space-x-4">
          <Link href="/processos">
            <Button variant="outline" size="sm">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Voltar
            </Button>
          </Link>
          <div>
            <h1 className="text-3xl font-bold text-foreground">Novo Processo</h1>
            <p className="text-muted-foreground mt-2">
              Cadastre um novo processo para acompanhamento
            </p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3 mt-4 sm:mt-0">
          <Badge variant="outline" className="text-xs">
            <Plus className="mr-1 h-3 w-3" />
            Novo Cadastro
          </Badge>
        </div>
      </div>

      {/* Layout Horizontal - Formulário + Resumo Fixo */}
      <div className="flex gap-6">
        {/* Formulário Principal - Rolável */}
        <div className="flex-1 space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Informações Básicas
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Número do Processo *
                  </label>
                  <Input
                    placeholder="Ex: 2024.001.001"
                    value={formData.numeroProcesso}
                    onChange={(e) => handleInputChange('numeroProcesso', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Tab') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="modalidade"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="numeroProcesso"
                    tabIndex={1}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Modalidade *
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background focus:ring-2 focus:ring-primary focus:border-transparent"
                    value={formData.modalidade}
                    onChange={(e) => handleInputChange('modalidade', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Tab') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="objeto"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="modalidade"
                    tabIndex={2}
                  >
                    <option value="">Selecione...</option>
                    <option value="Pregão Eletrônico">Pregão Eletrônico</option>
                    <option value="Pregão Presencial">Pregão Presencial</option>
                    <option value="Concorrência">Concorrência</option>
                    <option value="Tomada de Preços">Tomada de Preços</option>
                    <option value="Convite">Convite</option>
                    <option value="Dispensa">Dispensa</option>
                    <option value="Inexigibilidade">Inexigibilidade</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Objeto *
                </label>
                <textarea
                  className="w-full p-2 border border-input rounded-md bg-background min-h-[80px]"
                  placeholder="Descreva o objeto da licitação..."
                  value={formData.objeto}
                  onChange={(e) => handleInputChange('objeto', e.target.value)}
                  onKeyDown={(e) => {
                    if (e.key === 'Tab') {
                      e.preventDefault();
                      const nextElement = document.querySelector('[name="valorEstimado"]') as HTMLElement;
                      nextElement?.focus();
                    }
                  }}
                  name="objeto"
                  tabIndex={3}
                />
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Valor Estimado
                  </label>
                  <CurrencyInput
                    value={formData.valorEstimado}
                    onChange={(value) => handleInputChange('valorEstimado', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Tab') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="status"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Status
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background"
                    value={formData.status}
                    onChange={(e) => handleInputChange('status', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter' || e.key === 'Tab') {
                        e.preventDefault();
                        const nextElement = document.querySelector('[name="secretaria"]') as HTMLElement;
                        nextElement?.focus();
                      }
                    }}
                    name="status"
                    tabIndex={5}
                  >
                    <option value="Em Instrução">Em Instrução</option>
                    <option value="Para Pesquisa de Preços">Para Pesquisa de Preços</option>
                    <option value="Aguardando Documento do Fornecedor">Aguardando Documento do Fornecedor</option>
                    <option value="Para Análise Técnica">Para Análise Técnica</option>
                    <option value="Para Análise Orçamentária">Para Análise Orçamentária</option>
                    <option value="Retornou Após Análise Orçamentária">Retornou Após Análise Orçamentária</option>
                    <option value="Para Análise Jurídica">Para Análise Jurídica</option>
                    <option value="Retornou Após Análise Jurídica">Retornou Após Análise Jurídica</option>
                    <option value="Para Adequações">Para Adequações</option>
                    <option value="Encaminhado Para a Secretaria">Encaminhado Para a Secretaria</option>
                    <option value="Para Publicação de Edital">Para Publicação de Edital</option>
                    <option value="Aguardando Abertura da Licitação">Aguardando Abertura da Licitação</option>
                    <option value="Licitação em Andamento">Licitação em Andamento</option>
                    <option value="Para Elaboração de Contrato/Ata">Para Elaboração de Contrato/Ata</option>
                    <option value="Para Homologação e AUDESP">Para Homologação e AUDESP</option>
                    <option value="Para Assinatura da Homologação">Para Assinatura da Homologação</option>
                    <option value="Finalizado">Finalizado</option>
                    <option value="Para Abertura de Processos de Gerenciamento">Para Abertura de Processos de Gerenciamento</option>
                    <option value="Para Análise e Distribuição">Para Análise e Distribuição</option>
                    <option value="Para Ratificação da Pesquisa de Preços">Para Ratificação da Pesquisa de Preços</option>
                  </select>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="mr-2 h-5 w-5" />
                Responsabilidade
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Secretaria *
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background"
                    value={formData.secretaria}
                    onChange={(e) => handleInputChange('secretaria', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  >
                    <option value="">Selecione...</option>
                    <option value="SE">SE - Secretaria de Educação</option>
                    <option value="SMS">SMS - Secretaria Municipal de Saúde</option>
                    <option value="SMMA">SMMA - Secretaria de Meio Ambiente</option>
                    <option value="SMDU">SMDU - Secretaria de Desenvolvimento Urbano</option>
                    <option value="SGM">SGM - Secretaria de Governo Municipal</option>
                    <option value="SF">SF - Secretaria de Finanças</option>
                    <option value="SAJ">SAJ - Secretaria de Assuntos Jurídicos</option>
                    <option value="SMCAS">SMCAS - Secretaria de Cultura, Esporte e Lazer</option>
                    <option value="SMTRANS">SMTRANS - Secretaria de Transportes</option>
                    <option value="GABINETE">Gabinete do Prefeito</option>
                    <option value="CLMP">CLMP - Coordenadoria de Licitações</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Responsável
                  </label>
                  <Input
                    placeholder="Nome do responsável"
                    value={formData.responsavel}
                    onChange={(e) => handleInputChange('responsavel', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <DollarSign className="mr-2 h-5 w-5" />
                Fontes de Recursos
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0001 (TESOURO)
                  </label>
                  <CurrencyInput
                    value={formData.fonteTesouro}
                    onChange={(value) => handleInputChange('fonteTesouro', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0002 (ESTADUAL)
                  </label>
                  <CurrencyInput
                    value={formData.fonteEstadual}
                    onChange={(value) => handleInputChange('fonteEstadual', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0003 (FUNDO)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFundo}
                    onChange={(value) => handleInputChange('fonteFundo', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0005 (FEDERAL)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFederal}
                    onChange={(value) => handleInputChange('fonteFederal', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Fonte 0007 (FINISA)
                  </label>
                  <CurrencyInput
                    value={formData.fonteFinisa}
                    onChange={(value) => handleInputChange('fonteFinisa', value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>

              {/* Datas de Vencimento das Fontes Não-Tesouro */}
              {(formData.fonteEstadual || formData.fonteFundo || formData.fonteFederal || formData.fonteFinisa) && (
                <div className="mt-6 p-4 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                  <h4 className="text-sm font-semibold text-yellow-800 dark:text-yellow-200 mb-3 flex items-center">
                    <AlertTriangle className="mr-2 h-4 w-4" />
                    Datas de Vencimento dos Recursos Externos
                  </h4>
                  <p className="text-xs text-yellow-700 dark:text-yellow-300 mb-4">
                    Informe as datas de vencimento para controle de risco de perda dos recursos.
                    <strong>Não é obrigatório</strong> - você pode cadastrar o processo sem essas datas.
                  </p>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {formData.fonteEstadual && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Estadual
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoEstadual}
                          onChange={(e) => handleInputChange('dataVencimentoEstadual', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFundo && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Fundo
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFundo}
                          onChange={(e) => handleInputChange('dataVencimentoFundo', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFederal && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Federal
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFederal}
                          onChange={(e) => handleInputChange('dataVencimentoFederal', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}

                    {formData.fonteFinisa && (
                      <div>
                        <label className="text-sm font-medium text-foreground mb-2 block">
                          Data Vencimento - Fonte Finisa
                        </label>
                        <Input
                          type="date"
                          value={formData.dataVencimentoFinisa}
                          onChange={(e) => handleInputChange('dataVencimentoFinisa', e.target.value)}
                          className="bg-background"
                        />
                      </div>
                    )}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="mr-2 h-5 w-5" />
                Cronograma e Localização
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Data de Início do Processo
                  </label>
                  <Input
                    type="date"
                    value={formData.dataInicio}
                    onChange={(e) => handleInputChange('dataInicio', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Data Entrada na CLMP
                  </label>
                  <Input
                    type="date"
                    value={formData.dataEntradaCLMP}
                    onChange={(e) => handleInputChange('dataEntradaCLMP', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  />
                </div>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Prioridade
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background"
                    value={formData.prioridade}
                    onChange={(e) => handleInputChange('prioridade', e.target.value)}
                  >
                    <option value="Normal">Normal</option>
                    <option value="Alta">Alta</option>
                    <option value="Urgente">Urgente</option>
                  </select>
                </div>
                <div>
                  <label className="text-sm font-medium text-foreground mb-2 block">
                    Local Atual
                  </label>
                  <select
                    className="w-full p-2 border border-input rounded-md bg-background"
                    value={formData.local}
                    onChange={(e) => handleInputChange('local', e.target.value)}
                    onKeyDown={(e) => {
                      if (e.key === 'Enter') {
                        e.preventDefault();
                      }
                    }}
                  >
                    <option value="CLMP">CLMP</option>
                    <option value="SF">SF</option>
                    <option value="SAJ">SAJ</option>
                    <option value="SE">SE</option>
                    <option value="SMS">SMS</option>
                    <option value="SMMA">SMMA</option>
                    <option value="SMDU">SMDU</option>
                    <option value="SGM">SGM</option>
                    <option value="SMCAS">SMCAS</option>
                    <option value="SMTRANS">SMTRANS</option>
                    <option value="GABINETE">GABINETE</option>
                    <option value="AUDESP">AUDESP</option>
                    <option value="FORNECEDOR">FORNECEDOR</option>
                  </select>
                </div>
              </div>
              
              <div>
                <label className="text-sm font-medium text-foreground mb-2 block">
                  Observações
                </label>
                <textarea
                  className="w-full p-2 border border-input rounded-md bg-background min-h-[80px]"
                  placeholder="Observações adicionais..."
                  value={formData.observacoes}
                  onChange={(e) => handleInputChange('observacoes', e.target.value)}
                />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Sidebar com Resumo - FIXO NA TELA */}
        <div className="w-80 sticky top-6 h-fit space-y-6">
          <Card className="shadow-lg">
            <CardHeader className="bg-primary/5">
              <CardTitle className="text-lg flex items-center">
                <FileText className="mr-2 h-5 w-5" />
                Resumo do Processo
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-muted-foreground">Status do Formulário</span>
                  {isFormValid ? (
                    <Badge variant="success" className="text-xs">
                      <CheckCircle className="mr-1 h-3 w-3" />
                      Válido
                    </Badge>
                  ) : (
                    <Badge variant="destructive" className="text-xs">
                      <AlertCircle className="mr-1 h-3 w-3" />
                      Incompleto
                    </Badge>
                  )}
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Campos obrigatórios:</span>
                    <span className="font-medium">4/4</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Campos opcionais:</span>
                    <span className="font-medium">6</span>
                  </div>
                </div>
              </div>
              
              <div className="pt-4 border-t border-border">
                <Button 
                  onClick={handleSave}
                  disabled={!isFormValid || saving}
                  className="w-full"
                  size="lg"
                >
                  {saving ? (
                    <>
                      <Save className="mr-2 h-4 w-4 animate-spin" />
                      Salvando...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Salvar Processo
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="text-lg">Dicas</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3 text-sm">
              <div className="flex items-start space-x-2">
                <AlertCircle className="h-4 w-4 text-yellow-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  Preencha todos os campos obrigatórios marcados com *
                </p>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-green-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  O número do processo deve ser único no sistema
                </p>
              </div>
              <div className="flex items-start space-x-2">
                <CheckCircle className="h-4 w-4 text-blue-600 mt-0.5 flex-shrink-0" />
                <p className="text-muted-foreground">
                  Confira todos os dados antes de salvar o processo
                </p>
              </div>

              {/* Botão de Salvar - Sempre Visível */}
              <div className="pt-4 border-t">
                <Button
                  onClick={handleSave}
                  disabled={!isFormValid || saving}
                  className="w-full"
                  size="lg"
                >
                  {saving ? (
                    <>
                      <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                      Salvando Processo...
                    </>
                  ) : (
                    <>
                      <Save className="mr-2 h-4 w-4" />
                      Salvar Processo
                    </>
                  )}
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
